{"experiment_info": {"timestamp": "2024-05-23T23:00:00Z", "model": "mobilenetv4_conv_small", "deployment_type": "cpu", "batch_size": 1, "num_samples": 1000, "precision": "fp32"}, "avg_latency_ms": 89.7, "p50_latency_ms": 87.2, "p95_latency_ms": 134.5, "p99_latency_ms": 178.9, "max_latency_ms": 234.7, "min_latency_ms": 76.3, "samples_per_second": 11.1, "total_inference_time_ms": 89700, "throughput_images_per_sec": 11.1, "accuracy_metrics": {"top1_accuracy": 0.724, "top5_accuracy": 0.891, "total_correct_top1": 724, "total_correct_top5": 891, "total_samples": 1000}, "resource_utilization": {"avg_gpu_utilization": 0.0, "max_gpu_utilization": 0.0, "avg_gpu_memory_utilization": 0.0, "max_gpu_memory_utilization": 0.0, "avg_cpu_utilization": 85.4, "avg_memory_utilization": 45.2}, "system_info": {"gpu_model": "None", "gpu_memory_gb": 0, "cpu_cores": 8, "system_memory_gb": 32, "kubernetes_version": "1.24.0", "triton_version": "23.04"}}