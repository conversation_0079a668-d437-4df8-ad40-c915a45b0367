# Sample Results Data

This directory contains sample experimental results that allow you to generate plots and analysis without running the full experiments.

## Quick Start

```bash
# Generate all plots from sample data
make demo-plots

# Generate baseline workload plots
make demo-baseline-plots

# Generate RL training plots  
make demo-rl-plots

# Show sample data structure
make demo-data
```

## Data Structure

```
results/
├── baseline/           # GPU baseline results
│   ├── dynamic/       # Dynamic load test results
│   └── metrics/       # Synthetic test results
├── cpu_baseline/      # CPU baseline results  
│   ├── dynamic/       # Dynamic load test results
│   └── metrics/       # Synthetic test results
└── rl/                # RL training results
    └── training_*/    # Training session data
```

## Load Patterns

- **ramp**: Gradual load increase from 1 to 100 users
- **spike**: Sudden load burst from 10 to 150 users  
- **periodic**: Oscillating load between 20-80 users
- **random**: Random load variations 15-95 users

## Generated Figures

- `figures/baseline/workload_patterns.png` - Baseline workload visualization
- `figures/rl/training_progress.png` - RL training progress
- `figures/comparison/` - Performance comparisons (when available)

## Usage in Papers

These sample results demonstrate:
- GPU vs CPU performance differences
- RL training convergence
- Multi-pattern workload behavior
- Academic-quality visualization

Perfect for reproducing paper figures without full experimental setup.
