{"experiment_info": {"timestamp": "2024-05-23T22:40:00Z", "model": "mobilenetv4_conv_small", "deployment_type": "gpu", "batch_size": 1, "num_samples": 1000, "precision": "fp16"}, "performance_metrics": {"avg_latency_ms": 12.4, "p50_latency_ms": 11.8, "p95_latency_ms": 18.7, "p99_latency_ms": 24.3, "max_latency_ms": 31.2, "min_latency_ms": 8.9, "samples_per_second": 80.6, "total_inference_time_ms": 12400, "throughput_images_per_sec": 80.6}, "accuracy_metrics": {"top1_accuracy": 0.724, "top5_accuracy": 0.891, "total_correct_top1": 724, "total_correct_top5": 891, "total_samples": 1000}, "resource_utilization": {"avg_gpu_utilization": 78.9, "max_gpu_utilization": 95.2, "avg_gpu_memory_utilization": 34.2, "max_gpu_memory_utilization": 42.1, "avg_cpu_utilization": 15.3, "avg_memory_utilization": 28.7}, "system_info": {"gpu_model": "NVIDIA RTX 3080", "gpu_memory_gb": 10, "cpu_cores": 8, "system_memory_gb": 32, "kubernetes_version": "1.24.0", "triton_version": "23.04"}}