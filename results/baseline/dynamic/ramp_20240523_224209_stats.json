[{"timestamp": "2024-05-23T22:42:09Z", "user_count": 1, "stats": [{"name": "GET /v2/models/mobilenetv4/infer", "method": "POST", "num_requests": 5, "num_failures": 0, "avg_response_time": 23.4, "min_response_time": 18.2, "max_response_time": 31.7, "response_time_percentile_0.5": 22.1, "response_time_percentile_0.95": 29.8, "response_time_percentile_0.99": 31.5, "current_rps": 0.8}, {"name": "Aggregated", "method": null, "num_requests": 5, "num_failures": 0, "avg_response_time": 23.4, "min_response_time": 18.2, "max_response_time": 31.7, "response_time_percentile_0.5": 22.1, "response_time_percentile_0.95": 29.8, "response_time_percentile_0.99": 31.5, "current_rps": 0.8}], "current_response_time_percentiles": {"response_time_percentile_0.5": 22.1, "response_time_percentile_0.95": 29.8, "response_time_percentile_0.99": 31.5}}, {"timestamp": "2024-05-23T22:42:14Z", "user_count": 6, "stats": [{"name": "GET /v2/models/mobilenetv4/infer", "method": "POST", "num_requests": 30, "num_failures": 0, "avg_response_time": 28.7, "min_response_time": 19.1, "max_response_time": 45.3, "response_time_percentile_0.5": 27.2, "response_time_percentile_0.95": 42.1, "response_time_percentile_0.99": 44.8, "current_rps": 4.2}, {"name": "Aggregated", "method": null, "num_requests": 30, "num_failures": 0, "avg_response_time": 28.7, "min_response_time": 19.1, "max_response_time": 45.3, "response_time_percentile_0.5": 27.2, "response_time_percentile_0.95": 42.1, "response_time_percentile_0.99": 44.8, "current_rps": 4.2}], "current_response_time_percentiles": {"response_time_percentile_0.5": 27.2, "response_time_percentile_0.95": 42.1, "response_time_percentile_0.99": 44.8}}, {"timestamp": "2024-05-23T22:47:45Z", "user_count": 100, "stats": [{"name": "GET /v2/models/mobilenetv4/infer", "method": "POST", "num_requests": 26190, "num_failures": 45, "avg_response_time": 45.2, "min_response_time": 12.4, "max_response_time": 234.7, "response_time_percentile_0.5": 42.1, "response_time_percentile_0.95": 78.5, "response_time_percentile_0.99": 125.3, "current_rps": 87.3}, {"name": "Aggregated", "method": null, "num_requests": 26190, "num_failures": 45, "avg_response_time": 45.2, "min_response_time": 12.4, "max_response_time": 234.7, "response_time_percentile_0.5": 42.1, "response_time_percentile_0.95": 78.5, "response_time_percentile_0.99": 125.3, "current_rps": 87.3}], "current_response_time_percentiles": {"response_time_percentile_0.5": 42.1, "response_time_percentile_0.95": 78.5, "response_time_percentile_0.99": 125.3}}]