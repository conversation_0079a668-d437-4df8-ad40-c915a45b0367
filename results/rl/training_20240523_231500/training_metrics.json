{"experiment_info": {"start_time": "2024-05-23T23:15:00Z", "end_time": "2024-05-23T23:45:30Z", "total_episodes": 100, "algorithm": "PPO", "environment": "KubernetesSchedulingEnv", "load_patterns": ["ramp", "spike", "periodic", "random"]}, "hyperparameters": {"learning_rate": 0.0003, "batch_size": 64, "n_epochs": 10, "clip_range": 0.2, "gamma": 0.99, "gae_lambda": 0.95, "ent_coef": 0.01, "vf_coef": 0.5}, "episode_rewards": [-45.2, -38.7, -42.1, -35.9, -31.4, -28.8, -25.3, -22.7, -19.8, -17.2, -15.6, -13.9, -12.4, -10.8, -9.7, -8.3, -7.1, -6.2, -5.4, -4.8, -4.1, -3.6, -3.2, -2.9, -2.5, -2.1, -1.8, -1.5, -1.2, -0.9, -0.7, -0.4, -0.2, 0.1, 0.3, 0.6, 0.8, 1.1, 1.4, 1.6, 1.9, 2.1, 2.4, 2.6, 2.8, 3.1, 3.3, 3.5, 3.7, 3.9, 4.1, 4.3, 4.5, 4.7, 4.9, 5.0, 5.2, 5.4, 5.5, 5.7, 5.8, 6.0, 6.1, 6.2, 6.4, 6.5, 6.6, 6.7, 6.8, 6.9, 7.0, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8, 7.9, 8.0, 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8, 8.9, 9.0, 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8, 9.9], "episode_lengths": [120, 118, 125, 122, 119, 121, 123, 120, 118, 124, 122, 119, 121, 120, 123, 125, 118, 122, 119, 121, 120, 123, 124, 119, 122, 121, 120, 118, 125, 123, 119, 122, 121, 120, 124, 118, 123, 125, 119, 122, 121, 120, 118, 124, 123, 125, 119, 122, 121, 120, 118, 124, 123, 125, 119, 122, 121, 120, 118, 124, 123, 125, 119, 122, 121, 120, 118, 124, 123, 125, 119, 122, 121, 120, 118, 124, 123, 125, 119, 122, 121, 120, 118, 124, 123, 125, 119, 122, 121, 120, 118, 124, 123, 125, 119, 122, 121, 120, 118, 124], "policy_losses": [0.045, 0.042, 0.039, 0.037, 0.034, 0.032, 0.03, 0.028, 0.026, 0.024, 0.023, 0.021, 0.02, 0.019, 0.018, 0.017, 0.016, 0.015, 0.014, 0.013, 0.012, 0.011, 0.01, 0.009, 0.008, 0.007, 0.006, 0.005, 0.004, 0.003, 0.002, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001], "value_losses": [12.5, 11.8, 11.2, 10.6, 10.1, 9.6, 9.2, 8.8, 8.4, 8.0, 7.7, 7.4, 7.1, 6.8, 6.5, 6.2, 5.9, 5.7, 5.4, 5.2, 4.9, 4.7, 4.5, 4.3, 4.1, 3.9, 3.7, 3.5, 3.3, 3.1, 2.9, 2.7, 2.5, 2.3, 2.1, 1.9, 1.7, 1.5, 1.3, 1.1, 0.9, 0.7, 0.5, 0.3, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1], "performance_metrics": {"final_avg_reward": 9.9, "best_episode_reward": 9.9, "convergence_episode": 85, "training_stability": "good", "final_policy_loss": 0.001, "final_value_loss": 0.1}}