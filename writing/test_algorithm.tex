\documentclass{article}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{amsmath}

\begin{document}

\begin{algorithm}[htbp]
\caption{Test Algorithm}
\label{alg:test}
\begin{algorithmic}[1]
\STATE \textbf{Input:} Test input
\STATE \textbf{Output:} Test output
\FOR{$i = 1$ to $n$}
    \STATE Do something
\ENDFOR
\STATE \textbf{return} result
\end{algorithmic}
\end{algorithm}

\end{document}
