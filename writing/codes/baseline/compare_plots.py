#!/usr/bin/env python3
"""
Compare original and optimized workload pattern plots
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from pathlib import Path

def create_comparison_figure():
    """Create a comparison figure showing original vs optimized plots"""
    
    # Define paths
    original_dir = Path("/home/<USER>/allProjects/ecrl/writing/figs/baseline")
    optimized_dir = Path("/home/<USER>/allProjects/ecrl/writing/figs/baseline/optimized")
    
    # Original files (using the ones mentioned in the user's message)
    original_files = {
        'ramp': 'ramp_20250523_173843_plot.png',
        'spike': 'spike_20250523_180351_plot.png', 
        'periodic': 'periodic_20250523_183319_plot.png',
        'random': 'random_20250523_184818_plot.png'
    }
    
    # Optimized files
    optimized_files = {
        'ramp': 'ramp_optimized.png',
        'spike': 'spike_optimized.png',
        'periodic': 'periodic_optimized.png', 
        'random': 'random_optimized.png'
    }
    
    # Create comparison figure
    fig, axes = plt.subplots(4, 2, figsize=(16, 20))
    fig.suptitle('Workload Pattern Plots: Original vs Optimized', fontsize=20, y=0.98)
    
    patterns = ['ramp', 'spike', 'periodic', 'random']
    pattern_titles = ['Ramp Pattern', 'Spike Pattern', 'Periodic Pattern', 'Random Pattern']
    
    for i, (pattern, title) in enumerate(zip(patterns, pattern_titles)):
        # Original plot
        original_path = original_dir / original_files[pattern]
        if original_path.exists():
            img_orig = mpimg.imread(original_path)
            axes[i, 0].imshow(img_orig)
            axes[i, 0].set_title(f'{title} - Original', fontsize=14)
            axes[i, 0].axis('off')
        else:
            axes[i, 0].text(0.5, 0.5, f'Original {pattern}\nfile not found', 
                           ha='center', va='center', transform=axes[i, 0].transAxes)
            axes[i, 0].set_title(f'{title} - Original (Missing)', fontsize=14)
        
        # Optimized plot
        optimized_path = optimized_dir / optimized_files[pattern]
        if optimized_path.exists():
            img_opt = mpimg.imread(optimized_path)
            axes[i, 1].imshow(img_opt)
            axes[i, 1].set_title(f'{title} - Optimized', fontsize=14)
            axes[i, 1].axis('off')
        else:
            axes[i, 1].text(0.5, 0.5, f'Optimized {pattern}\nfile not found', 
                           ha='center', va='center', transform=axes[i, 1].transAxes)
            axes[i, 1].set_title(f'{title} - Optimized (Missing)', fontsize=14)
    
    # Add column headers
    axes[0, 0].text(0.5, 1.1, 'Original (Small fonts, dense labels)', 
                    ha='center', va='bottom', transform=axes[0, 0].transAxes, 
                    fontsize=16, fontweight='bold')
    axes[0, 1].text(0.5, 1.1, 'Optimized (Large fonts, sparse labels)', 
                    ha='center', va='bottom', transform=axes[0, 1].transAxes, 
                    fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.95)
    
    # Save comparison
    output_path = Path("/home/<USER>/allProjects/ecrl/writing/figs/baseline/plot_comparison.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Comparison figure saved to: {output_path}")

def print_optimization_summary():
    """Print a summary of the optimizations made"""
    print("\n" + "="*60)
    print("WORKLOAD PATTERN PLOT OPTIMIZATION SUMMARY")
    print("="*60)
    print("\nPROBLEMS IDENTIFIED:")
    print("• X-axis labels too dense and overlapping")
    print("• Font sizes too small for academic publication")
    print("• Poor readability when scaled down in papers")
    print("• Inconsistent visual presentation")
    
    print("\nOPTIMIZATIONS APPLIED:")
    print("• Increased font sizes:")
    print("  - Title: 20pt (was default ~12pt)")
    print("  - Axis labels: 16pt (was default ~10pt)")
    print("  - Tick labels: 14pt (was default ~8pt)")
    print("• Reduced x-axis label density:")
    print("  - Show max 10 time points instead of all points")
    print("  - Intelligent spacing based on data length")
    print("  - Always include first and last points")
    print("• Enhanced visual elements:")
    print("  - Larger line width (3pt vs 2pt)")
    print("  - Larger markers (8pt vs 6pt)")
    print("  - White marker edges for better contrast")
    print("• Improved layout:")
    print("  - Larger figure size (12x8 vs 12x6)")
    print("  - Better padding and spacing")
    print("  - High DPI output (300 DPI)")
    
    print("\nFILES GENERATED:")
    optimized_dir = Path("/home/<USER>/allProjects/ecrl/writing/figs/baseline/optimized")
    if optimized_dir.exists():
        for file in sorted(optimized_dir.glob("*.png")):
            print(f"• {file.name}")
    
    print("\nLATEX UPDATES:")
    print("• Updated figures.tex to use optimized plots")
    print("• Removed duplicate CPU version plots")
    print("• Updated captions to reflect improvements")
    print("• Maintained consistent labeling scheme")
    
    print("\nBENEFITS:")
    print("• Much better readability in academic papers")
    print("• Professional appearance suitable for publication")
    print("• Consistent with academic figure standards")
    print("• Reduced visual clutter")
    print("• Improved accessibility")
    print("="*60)

if __name__ == "__main__":
    print("Creating comparison figure...")
    create_comparison_figure()
    print_optimization_summary()
