#!/usr/bin/env python3
"""
Regenerate workload pattern plots with optimized font sizes and cleaner x-axis labels
for academic paper use.
"""

import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import os

def load_workload_data(users_file, stats_file):
    """Load workload data from separate users and stats JSON files"""
    if not os.path.exists(users_file) or not os.path.exists(stats_file):
        print(f"Warning: {users_file} or {stats_file} not found")
        return None, None, None

    # Load user count data
    with open(users_file, 'r') as f:
        users_data = json.load(f)

    timestamps = users_data.get('timestamps', [])
    user_counts = users_data.get('user_counts', [])

    # Load stats data
    with open(stats_file, 'r') as f:
        stats_data = json.load(f)

    # Extract P95 response times from aggregated stats
    response_times = []
    for stats_entry in stats_data:
        if 'current_response_time_percentiles' in stats_entry:
            p95_time = stats_entry['current_response_time_percentiles'].get('response_time_percentile_0.95', None)
            response_times.append(p95_time)
        else:
            response_times.append(None)

    # Ensure all arrays have the same length (use the minimum length)
    min_length = min(len(timestamps), len(user_counts), len(response_times))
    timestamps = timestamps[:min_length]
    user_counts = user_counts[:min_length]
    response_times = response_times[:min_length]

    return timestamps, user_counts, response_times

def create_optimized_plot(user_counts, response_times, pattern_name, output_file):
    """Create an optimized plot with better readability for academic papers"""
    
    if not user_counts:
        print(f"No data for {pattern_name}")
        return
    
    # Create time labels - show only every nth point to reduce clutter
    n_points = len(user_counts)
    if n_points <= 10:
        step = 1
    elif n_points <= 20:
        step = 2
    else:
        step = max(1, n_points // 10)  # Show about 10 labels max
    
    # Create generic time labels
    time_labels = [f't{i+1}' for i in range(n_points)]
    
    # Create figure with optimal size for clean presentation
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # Set larger font sizes globally for this plot
    plt.rcParams.update({
        'font.size': 14,
        'axes.titlesize': 18,
        'axes.labelsize': 16,
        'xtick.labelsize': 14,
        'ytick.labelsize': 14,
        'legend.fontsize': 16
    })
    
    # Plot user count on left y-axis
    color = 'tab:blue'
    ax1.set_xlabel('Time Points', fontsize=16)
    ax1.set_ylabel('User Count', color=color, fontsize=16)
    line1 = ax1.plot(range(n_points), user_counts, color=color, marker='o',
             linewidth=3, markersize=8, markerfacecolor=color, markeredgecolor='white', markeredgewidth=1,
             label='User Count')
    ax1.tick_params(axis='y', labelcolor=color, labelsize=14)
    ax1.grid(True, alpha=0.3)

    # Set Y-axis limit to provide space for legend at top
    max_users = max(user_counts) if user_counts else 100
    ax1.set_ylim(0, max(120, max_users * 1.3))  # Ensure at least 120 or 30% above max
    
    # Set x-axis ticks to show only selected labels
    tick_positions = list(range(0, n_points, step))
    if tick_positions[-1] != n_points - 1:  # Ensure last point is shown
        tick_positions.append(n_points - 1)
    
    ax1.set_xticks(tick_positions)
    ax1.set_xticklabels([time_labels[i] for i in tick_positions], rotation=45)
    
    # Plot response time on right y-axis if available
    if response_times and any(rt is not None for rt in response_times):
        ax2 = ax1.twinx()
        color = 'tab:red'
        ax2.set_ylabel('P95 Response Time (ms)', color=color, fontsize=16)
        line2 = ax2.plot(range(n_points), response_times, color=color, marker='x',
                linewidth=3, markersize=8, label='P95 Response Time (ms)')
        ax2.tick_params(axis='y', labelcolor=color, labelsize=14)

        # Set Y-axis limit for response time to provide space for legend at top
        max_response = max([rt for rt in response_times if rt is not None]) if response_times else 6000
        ax2.set_ylim(0, max(7000, max_response * 1.3))  # Ensure at least 7000 or 30% above max

        # All legends in upper left corner with smaller font and compact layout
        # Combine lines from both axes for legend
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=12,
                  frameon=True, fancybox=True, shadow=True, framealpha=0.9,
                  bbox_to_anchor=(0.02, 0.98))  # Fine-tune position

    # Add title with larger font
    plt.title(f'{pattern_name.title()} Load Pattern', fontsize=20, pad=20)
    
    # Adjust layout to prevent label cutoff
    fig.tight_layout()
    
    # Save with high DPI for academic use
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # Reset font parameters
    plt.rcParams.update(plt.rcParamsDefault)
    
    print(f"Generated optimized plot: {output_file}")

def main():
    """Main function to regenerate all workload pattern plots"""
    
    # Define patterns and their corresponding data files
    patterns = ['ramp', 'spike', 'periodic', 'random']
    
    # Input directory (baseline results)
    baseline_dir = Path("/home/<USER>/allProjects/ecrl/experiments/results/baseline")
    
    # Output directory for optimized plots
    output_dir = Path("/home/<USER>/allProjects/ecrl/writing/figs/baseline/optimized")
    output_dir.mkdir(exist_ok=True)
    
    print("Regenerating workload pattern plots with optimized readability...")
    
    for pattern in patterns:
        print(f"\nProcessing {pattern} pattern...")

        # Find the most recent users and stats files for this pattern
        users_files = list(baseline_dir.glob(f"dynamic/{pattern}_*_users.json"))
        stats_files = list(baseline_dir.glob(f"dynamic/{pattern}_*_stats.json"))

        if not users_files or not stats_files:
            print(f"No data files found for {pattern} pattern")
            continue

        # Use the most recent files (should have matching timestamps)
        latest_users_file = max(users_files, key=lambda x: x.stat().st_mtime)
        # Find corresponding stats file with same timestamp
        timestamp = latest_users_file.stem.replace(f"{pattern}_", "").replace("_users", "")
        stats_file = baseline_dir / f"dynamic/{pattern}_{timestamp}_stats.json"

        if not stats_file.exists():
            print(f"No matching stats file found for {pattern} pattern")
            continue

        print(f"Using data from: {latest_users_file} and {stats_file}")

        # Load data
        timestamps, user_counts, response_times = load_workload_data(latest_users_file, stats_file)

        if user_counts:
            # Generate optimized plot
            output_file = output_dir / f"{pattern}_optimized.png"
            create_optimized_plot(user_counts, response_times, pattern, output_file)
        else:
            print(f"No valid data found for {pattern}")
    
    print(f"\nAll optimized plots saved to: {output_dir}")
    print("\nGenerated files:")
    for pattern in patterns:
        output_file = output_dir / f"{pattern}_optimized.png"
        if output_file.exists():
            print(f"- {pattern}_optimized.png")

if __name__ == "__main__":
    main()
