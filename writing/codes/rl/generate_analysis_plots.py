#!/usr/bin/env python3
"""
Generate individual analysis plots from RL training results
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
import os

def load_rl_results(training_dir):
    """Load RL training results"""
    training_path = Path(training_dir)
    
    # Load training progress
    progress_file = training_path / "training_progress.json"
    with open(progress_file, 'r') as f:
        progress = json.load(f)
    
    # Load final evaluation
    eval_file = training_path / "final_evaluation.json"
    with open(eval_file, 'r') as f:
        evaluation = json.load(f)
    
    return progress, evaluation

def analyze_pattern_performance(evaluation):
    """Analyze performance by load pattern"""
    pattern_stats = {}
    
    for pattern, results in evaluation.items():
        rewards = [r.get('episode_reward', 0) for r in results if isinstance(r, dict)]
        
        if rewards:
            pattern_stats[pattern] = {
                'avg_reward': np.mean(rewards),
                'std_reward': np.std(rewards),
                'min_reward': np.min(rewards),
                'max_reward': np.max(rewards),
                'count': len(rewards)
            }
    
    return pattern_stats

def generate_analysis_plots(progress, pattern_stats, output_dir):
    """Generate individual analysis plots"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Set larger font sizes for better readability
    plt.rcParams.update({
        'font.size': 14, 
        'legend.fontsize': 16, 
        'axes.titlesize': 18,
        'axes.labelsize': 16,
        'xtick.labelsize': 14,
        'ytick.labelsize': 14
    })
    
    rewards = progress['episode_rewards']
    
    # 1. Training Progress with Moving Average (same as first subplot)
    plt.figure(figsize=(10, 6))
    episodes = range(1, len(rewards) + 1)
    plt.plot(episodes, rewards, alpha=0.7, label='Episode Rewards', linewidth=1.5, color='steelblue')
    
    # Moving average
    window_size = 10
    if len(rewards) >= window_size:
        moving_avg = []
        for i in range(len(rewards) - window_size + 1):
            moving_avg.append(np.mean(rewards[i:i+window_size]))
        
        plt.plot(range(window_size, len(rewards) + 1), moving_avg, 
                'r-', linewidth=3, label=f'Moving Avg ({window_size})')
    
    plt.xlabel('Episode', fontsize=16)
    plt.ylabel('Reward', fontsize=16)
    plt.title('RL Training Progress', fontsize=18)
    plt.legend(fontsize=16)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_path / 'rl_training_progress.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Reward Distribution (same as second subplot)
    plt.figure(figsize=(8, 6))
    plt.hist(rewards, bins=20, alpha=0.7, edgecolor='black', color='steelblue')
    plt.xlabel('Reward', fontsize=16)
    plt.ylabel('Frequency', fontsize=16)
    plt.title('Reward Distribution', fontsize=18)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_path / 'rl_reward_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Performance by Load Pattern (same as third subplot)
    if pattern_stats:
        patterns = list(pattern_stats.keys())
        avg_rewards = [pattern_stats[p]['avg_reward'] for p in patterns]
        std_rewards = [pattern_stats[p]['std_reward'] for p in patterns]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(patterns, avg_rewards, yerr=std_rewards, capsize=5, alpha=0.7, color='lightcoral')
        plt.xlabel('Load Pattern', fontsize=16)
        plt.ylabel('Average Reward', fontsize=16)
        plt.title('Performance by Load Pattern', fontsize=18)
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, avg, std in zip(bars, avg_rewards, std_rewards):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.05,
                    f'{avg:.3f}±{std:.3f}', ha='center', va='bottom', fontsize=12)
        
        plt.tight_layout()
        plt.savefig(output_path / 'rl_pattern_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 4. Learning Progress by Pattern (same as fourth subplot)
    if 'episode_summaries' in progress:
        pattern_colors = {'random': 'blue', 'ramp': 'green', 'spike': 'red', 'periodic': 'orange'}
        
        plt.figure(figsize=(10, 6))
        
        # Plot points by pattern
        for i, episode in enumerate(progress['episode_summaries']):
            pattern = episode['load_pattern']
            reward = episode['episode_reward']
            color = pattern_colors.get(pattern, 'gray')
            plt.scatter(i+1, reward, c=color, alpha=0.7, s=50)
        
        plt.xlabel('Episode', fontsize=16)
        plt.ylabel('Reward', fontsize=16)
        plt.title('Learning Progress by Pattern', fontsize=18)
        
        # Create legend with larger markers
        legend_elements = []
        for pattern, color in pattern_colors.items():
            if any(ep['load_pattern'] == pattern for ep in progress['episode_summaries']):
                legend_elements.append(plt.scatter([], [], c=color, label=pattern, s=100))
        
        plt.legend(fontsize=16, markerscale=1.5)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'rl_learning_by_pattern.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # Reset font sizes
    plt.rcParams.update(plt.rcParamsDefault)
    
    print(f"Analysis plots saved to {output_path}")
    print("Generated files:")
    print("- rl_training_progress.png")
    print("- rl_reward_distribution.png") 
    print("- rl_pattern_performance.png")
    print("- rl_learning_by_pattern.png")

def main():
    # Find the latest training results
    rl_dir = Path("/home/<USER>/allProjects/ecrl/experiments/rl")
    training_dirs = [d for d in rl_dir.glob("training_*") if d.is_dir()]
    
    if not training_dirs:
        print("No training directories found!")
        return
    
    # Use the latest training directory
    latest_training = max(training_dirs, key=lambda x: x.stat().st_mtime)
    print(f"Using training results from: {latest_training}")
    
    # Output directory
    output_dir = "/home/<USER>/allProjects/ecrl/writing/figs/rl/individual"
    
    try:
        print("Loading RL training results...")
        progress, evaluation = load_rl_results(latest_training)
        
        # Analyze pattern-specific performance
        pattern_stats = analyze_pattern_performance(evaluation)
        
        # Generate analysis plots
        generate_analysis_plots(progress, pattern_stats, output_dir)
        
        print("Analysis plots generation completed successfully!")
        
    except Exception as e:
        print(f"Error generating plots: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
