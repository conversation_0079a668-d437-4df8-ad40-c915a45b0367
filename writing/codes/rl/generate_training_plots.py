#!/usr/bin/env python3
"""
Generate individual plots from RL training_progress.png for paper inclusion
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
import os

def load_training_data(training_dir):
    """Load training data from the training directory"""
    training_path = Path(training_dir)
    
    # Load training progress
    progress_file = training_path / "training_progress.json"
    with open(progress_file, 'r') as f:
        progress = json.load(f)
    
    return progress

def generate_training_individual_plots(progress, output_dir):
    """Generate individual plots from training progress data"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Set larger font sizes for better readability
    plt.rcParams.update({
        'font.size': 16,
        'legend.fontsize': 18,
        'axes.titlesize': 20,
        'axes.labelsize': 18,
        'xtick.labelsize': 16,
        'ytick.labelsize': 16
    })
    
    episode_rewards = progress['episode_rewards']
    episode_lengths = progress.get('episode_lengths', [])
    training_metrics = progress.get('training_metrics', [])
    episode_summaries = progress.get('episode_summaries', [])
    
    episodes = range(1, len(episode_rewards) + 1)
    
    # 1. Training Rewards (Episode Rewards + Moving Average)
    plt.figure(figsize=(10, 6))
    plt.plot(episodes, episode_rewards, alpha=0.7, label='Episode Rewards', linewidth=1.5, color='steelblue')
    
    if len(episode_rewards) >= 10:
        # Moving average
        window = min(10, len(episode_rewards) // 2)
        moving_avg = np.convolve(episode_rewards, np.ones(window)/window, mode='valid')
        plt.plot(range(window, len(episode_rewards) + 1), moving_avg,
                color='red', linewidth=3, label=f'Moving Avg ({window})')
    
    plt.xlabel('Episode', fontsize=18)
    plt.ylabel('Reward', fontsize=18)
    # Remove title for cleaner appearance
    plt.legend(fontsize=18)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_path / 'training_rewards.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Episode Lengths
    if episode_lengths:
        plt.figure(figsize=(10, 6))
        plt.plot(episodes, episode_lengths, alpha=0.7, color='green', linewidth=1.5)
        plt.xlabel('Episode', fontsize=18)
        plt.ylabel('Episode Length', fontsize=18)
        # Remove title for cleaner appearance
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'episode_lengths.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 3. Training Losses (if available)
    if training_metrics:
        policy_losses = [m.get('policy_loss', 0) for m in training_metrics if m and isinstance(m, dict)]
        value_losses = [m.get('value_loss', 0) for m in training_metrics if m and isinstance(m, dict)]
        
        if policy_losses and any(loss > 0 for loss in policy_losses):
            plt.figure(figsize=(10, 6))
            updates = range(1, len(policy_losses) + 1)
            plt.plot(updates, policy_losses, label='Policy Loss', alpha=0.7, linewidth=2, color='orange')
            plt.plot(updates, value_losses, label='Value Loss', alpha=0.7, linewidth=2, color='purple')
            plt.xlabel('Update', fontsize=18)
            plt.ylabel('Loss', fontsize=18)
            # Remove title for cleaner appearance
            plt.legend(fontsize=18)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(output_path / 'training_losses.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    # 4. Performance by Load Pattern
    if episode_summaries:
        pattern_rewards = {}
        for summary in episode_summaries:
            if isinstance(summary, dict):
                pattern = summary.get('load_pattern', 'unknown')
                reward = summary.get('episode_reward', 0)
                if pattern not in pattern_rewards:
                    pattern_rewards[pattern] = []
                pattern_rewards[pattern].append(reward)
        
        if pattern_rewards:
            patterns = list(pattern_rewards.keys())
            avg_rewards = [np.mean(pattern_rewards[p]) for p in patterns]
            std_rewards = [np.std(pattern_rewards[p]) for p in patterns]
            
            plt.figure(figsize=(10, 6))
            bars = plt.bar(patterns, avg_rewards, alpha=0.7, color='steelblue')  # Remove error bars completely
            # Remove x-axis label for cleaner appearance
            plt.ylabel('Average Reward', fontsize=20)
            # Remove title for cleaner appearance
            plt.xticks(rotation=45, fontsize=18)
            plt.yticks(fontsize=18)
            plt.grid(True, alpha=0.3)

            # Set y-axis format to 1 decimal place
            from matplotlib.ticker import FormatStrFormatter
            plt.gca().yaxis.set_major_formatter(FormatStrFormatter('%.1f'))

            # Calculate y-axis limit to accommodate labels
            max_height = max(avg_rewards)  # No need to add std since no error bars
            plt.ylim(0, max_height * 1.3)  # Add 30% padding for labels

            # Add value labels on bars with larger font (remove std deviation)
            for bar, avg in zip(bars, avg_rewards):
                label_y = bar.get_height() + (max_height * 0.05)  # Proportional offset
                plt.text(bar.get_x() + bar.get_width()/2, label_y,
                        f'{avg:.3f}', ha='center', va='bottom', fontsize=14)
            
            plt.tight_layout()
            plt.savefig(output_path / 'pattern_performance_training.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    # Reset font sizes
    plt.rcParams.update(plt.rcParamsDefault)
    
    print(f"Training individual plots saved to {output_path}")
    print("Generated files:")
    print("- training_rewards.png")
    if episode_lengths:
        print("- episode_lengths.png")
    if training_metrics:
        print("- training_losses.png")
    if episode_summaries:
        print("- pattern_performance_training.png")

def main():
    # Find the latest training results
    rl_dir = Path("/home/<USER>/allProjects/ecrl/experiments/rl")
    training_dirs = [d for d in rl_dir.glob("training_*") if d.is_dir()]
    
    if not training_dirs:
        print("No training directories found!")
        return
    
    # Use the latest training directory
    latest_training = max(training_dirs, key=lambda x: x.stat().st_mtime)
    print(f"Using training results from: {latest_training}")
    
    # Output directory
    output_dir = "/home/<USER>/allProjects/ecrl/writing/figs/rl/individual"
    
    try:
        print("Loading training data...")
        progress = load_training_data(latest_training)
        
        # Generate individual plots
        generate_training_individual_plots(progress, output_dir)
        
        print("Training individual plots generation completed successfully!")
        
    except Exception as e:
        print(f"Error generating plots: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
