% [段1：引出背景，说明GPU推理服务与自动扩缩容在现代容器系统中的重要性]
The increasing demand for low-latency inference in AI applications has led to the widespread deployment of deep learning models on GPU-enabled containerized platforms. Kubernetes, as a de facto standard for container orchestration, provides mechanisms such as the Horizontal Pod Autoscaler (HPA) to dynamically scale services based on system metrics like CPU and memory utilization. However, when it comes to GPU-based inference workloads, the default autoscaling strategy often fails to capture the true resource bottlenecks, leading to either under-utilization or unnecessary over-provisioning.

% [段2：引出挑战，指出当前策略（如HPA）的局限性，形成问题空间]
This challenge is exacerbated in edge-like environments with limited GPU resources, where efficient utilization is critical. The static nature of threshold-based HPA does not adapt well to fluctuating workloads, especially for latency-sensitive services like MobileNetV4 inference. Moreover, these autoscaling decisions are typically agnostic to end-to-end application performance such as request latency or quality-of-service (QoS) violations.

% [段3：提出我们的研究问题（用RL智能调度替代传统HPA），承接挑战]
To address these limitations, we explore the use of reinforcement learning (RL) to replace traditional autoscaling mechanisms with an adaptive agent that learns to orchestrate GPU resources based on observed system-level and application-level feedback. By formulating the autoscaling problem as a sequential decision-making process, an RL agent can continuously observe system states, apply control actions (e.g., scaling replica count), and receive reward signals based on latency and resource efficiency.

% [段4：介绍我们系统的实验环境和部署亮点，转向我们方法的优势和可行性]
In this work, we build a fully local yet production-like experimental environment leveraging Ubuntu 24.04, NVIDIA RTX 3080, and Kind (Kubernetes-in-Docker). This setup enables realistic GPU utilization, Prometheus-based monitoring, and flexible workload deployment without relying on simulation. Our framework supports both baseline evaluations using standard HPA policies and RL agent training and testing cycles with full observability.

% [段5：概述本文贡献，形成结构预览，结束introduction]
We make the following contributions in this paper:
\begin{itemize} [label=$\blacksquare$, leftmargin=*]
  \item We design and implement an end-to-end RL-based resource orchestration framework for GPU-enabled Kubernetes inference services.
  \item We construct a realistic local environment using real GPU hardware and Kind, enabling accurate and reproducible performance evaluation.
  \item We conduct extensive experiments comparing our RL-based approach with traditional HPA across multiple load profiles, demonstrating improved latency and resource utilization.
  \item We analyze the trade-offs, limitations, and potential for extending our method to real-world edge clusters.
\end{itemize}