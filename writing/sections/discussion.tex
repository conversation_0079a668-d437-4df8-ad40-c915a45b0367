

% [段1：基于实验结果的关键发现和启示]
Our comprehensive baseline evaluation reveals several critical insights that inform the design of RL-based scheduling improvements. The observed performance variations across load patterns (up to 1.96x difference between GPU and CPU under random loads) demonstrate that intelligent, load-aware scheduling can provide significant benefits over static resource allocation strategies.

% [段2：负载模式敏感性的深入分析]
The load pattern sensitivity observed in our results highlights a key limitation of current Kubernetes scheduling approaches. While the default scheduler performs adequately under steady conditions, it lacks the predictive capabilities to optimize resource allocation based on workload characteristics. Our findings show that GPU acceleration provides the most significant benefits under irregular and burst load patterns, suggesting that an RL agent could learn to dynamically assign workloads to appropriate resource types based on predicted load characteristics.

% [段3：基于基线数据的RL设计指导]
The established performance benchmarks provide crucial guidance for RL agent design. The 100\% success rate across all 4,188-4,580 requests per test scenario establishes reliability requirements, while the P95 latency ranges (370ms to 6700ms) define performance targets. The consistent system stability observed across all load patterns suggests that RL agents can focus on optimization rather than basic system reliability, allowing for more aggressive exploration strategies during training.

% [段4：实验方法论的贡献和局限性]
Our experimental methodology contributes a comprehensive framework for evaluating Kubernetes scheduling performance under diverse load conditions. The four distinct load patterns (ramp, spike, periodic, random) provide a robust testing suite that captures real-world traffic variations. However, the single-node environment with logical partitions, while enabling controlled experimentation, may not fully capture the complexity of multi-node scheduling decisions and network latency considerations in distributed environments.

% [段3：扩展性问题——当前是单节点，后续需要支持多节点、异构GPU、边缘部署]
Currently, our experiments are conducted in a single-node Kind cluster with one GPU. Real-world deployments often involve heterogeneous resources, multi-node clusters, and geographically distributed edge nodes. Adapting our framework to such environments introduces new challenges in coordination, latency tolerance, and communication overhead.

% [段5：RL训练结果分析和验证]
Our RL training experiments successfully demonstrate the feasibility and effectiveness of learning-based resource scheduling. The 11.6\% performance improvement over 100 episodes, combined with pattern-specific learning behaviors, validates our approach's core hypothesis. The agent's ability to distinguish between four distinct reward levels (0.530, 1.761, 2.160, 2.710) corresponding to different load patterns indicates successful strategy diversification rather than convergence to a single policy.

The pattern-specific performance variations (Periodic: 2.081 ± 0.160, Random: 1.618 ± 0.911) align with our baseline findings, where predictable loads (periodic) enable better optimization than unpredictable ones (random). This consistency between baseline observations and RL learning patterns strengthens the validity of our approach.

% [段6：系统约束对RL训练的影响]
The resource constraints encountered during our experiments (1 GPU pod vs 3 CPU pods) reveal important considerations for real-world RL deployment. Our agent's successful adaptation to these constraints through graceful degradation and mock data fallback demonstrates the robustness required for production environments. The 100\% episode completion rate despite system limitations indicates that RL agents can maintain training stability even under resource pressure.

However, the reliance on mock data for training highlights a critical limitation: RL agents require real feedback loops to achieve optimal performance. While our theoretical performance improvements (2.6x-6.7x across different patterns) are promising, validation with real workloads remains essential for production deployment.

% [段7：RL方法论的贡献和局限性]
Our RL framework contributes several methodological innovations: (1) direct Kubernetes API integration for real-time control, (2) multi-objective reward function balancing latency, throughput, and efficiency, and (3) pattern-aware training with cyclic load rotation. The PPO-based approach with 137,998 parameters demonstrates that relatively compact models can learn effective scheduling policies.

The training convergence within 100 episodes suggests computational efficiency suitable for online learning scenarios. However, the current single-node limitation and mock data dependency constrain the generalizability of learned policies to diverse production environments.

% [段8：未来研究方向和实际部署考虑]
Future work should address several key areas: (1) multi-node cluster deployment to validate scalability, (2) integration with real inference workloads to eliminate mock data dependency, and (3) comparison with advanced baseline methods beyond default Kubernetes scheduling. The established RL framework provides a solid foundation for these extensions.

The theoretical performance improvements demonstrated in our experiments (2.3x-6.7x across patterns) warrant investigation in production environments. Additionally, the pattern recognition capabilities suggest potential for predictive scheduling based on historical load characteristics.

% [段9：向生产环境扩展的挑战]
Transitioning from our controlled experimental environment to production deployments presents several challenges. Multi-node clusters introduce network latency and resource heterogeneity considerations not captured in our single-node setup. The RL agent must learn to handle node failures, resource contention, and varying network conditions while maintaining performance guarantees.

Integration with existing Kubernetes ecosystem components (HPA, VPA, cluster autoscaler) requires careful coordination to avoid conflicting scaling decisions. Our RL framework's modular design and API-based integration provide a foundation for such coordination, but extensive testing in production environments remains necessary.

