% [段1：介绍Kubernetes资源调度与HPA的原理，为后续对比打基础]
Kubernetes has become the standard platform for managing containerized applications at scale. To support dynamic resource provisioning, it includes a built-in component known as the Horizontal Pod Autoscaler (HPA), which adjusts the number of pod replicas based on predefined thresholds of resource utilization, most commonly CPU or memory. HPA offers a simple and general mechanism for scaling, making it attractive for many workloads.

% [段2：指出HPA的问题，特别是它不适合GPU推理服务]
However, HPA has several limitations when applied to GPU-accelerated inference services. First, GPU usage is not a native scaling metric in the default Kubernetes setup. Even with extensions, the GPU utilization does not always correlate well with application performance, especially when workloads are bursty or exhibit queuing behavior. Second, HPA's threshold-based policies are reactive and static, making them suboptimal in dynamically changing conditions.

% [段3：承上启下，引出使用RL的动机，说明为何智能策略比HPA更适合]
These challenges motivate the need for a more intelligent, adaptive orchestration strategy. Reinforcement learning (RL), by learning policies through interaction with the environment, offers a promising alternative. RL can capture complex, time-dependent relationships between load intensity, resource allocation, and application-level performance, enabling proactive and performance-aware scaling.

% [段4：讲述使用RL的相关研究背景，表明本工作与现有研究的联系与差异]
RL-based resource management has shown promise in cloud and data center environments, such as VM placement, autoscaling, and scheduling. However, most prior work relies on simulated environments or lacks focus on GPU-based workloads. Our work bridges this gap by deploying a full RL-driven orchestration system in a realistic, GPU-enabled Kubernetes environment and evaluating it with real inference workloads.

% [段5：总结动机，铺垫我们的贡献]
In summary, while existing mechanisms such as HPA provide a useful baseline, they fall short under the requirements of modern AI inference services. This motivates our approach: building and evaluating an RL-based resource orchestration framework that directly learns to improve end-to-end performance in GPU-accelerated Kubernetes systems.
