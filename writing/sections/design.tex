% [段1：概述整个系统结构，并引导读者看图，提供整体感]
Figure~\ref{fig:system-overview} illustrates the architecture of our RL-based resource orchestration framework, which is deployed on a local machine running Ubuntu 24.04 with an NVIDIA RTX 3080 GPU. The system consists of three main components: the Kubernetes cluster environment, the monitoring and workload modules, and the reinforcement learning agent.

% [段2：详细介绍Kubernetes集群的构成和部署情况，交代实验环境]
We use Kind (Kubernetes-in-Docker) to deploy a GPU-enabled Kubernetes cluster locally. The cluster runs inside Docker containers but has access to the host's physical GPU through the NVIDIA container toolkit and GPU operator. This setup allows us to evaluate GPU inference workloads in a realistic yet controllable environment, without the need for cloud infrastructure or simulation.

% [段3：介绍监控模块（Prometheus/Grafana）和其数据来源、作用]
To monitor the system, we deploy Prometheus and DCGM-Exporter within the Kind cluster. These tools collect fine-grained resource metrics, including GPU utilization, memory usage, and pod-level statistics. Grafana is also included for real-time visualization, although it is not directly used by the RL agent. Prometheus exposes its metrics via REST APIs, which are queried by the agent during training and evaluation.

% [段4：介绍工作负载，包括MobileNetV4推理服务与Load Generator的部署方式]
Our target workload is an inference service based on MobileNetV4, deployed as a Kubernetes Deployment with GPU resource requests. A load generator, either running in the host or inside the cluster, sends inference requests at varying intensities (low, high, and mixed profiles). The goal is to observe how different orchestration strategies affect end-to-end latency and resource utilization under these workloads.

% [段5：介绍RL Agent结构和交互逻辑（state, action, reward），为后文训练做准备]
The PPO-based RL agent operates outside the cluster and interacts with it via the Kubernetes API and Prometheus API. At each decision step, the agent observes a set of system-level states (e.g., current replica count, GPU utilization, request latency). It then decides an action, such as scaling the number of inference replicas. After the action is applied, the system runs for a fixed interval, and the agent calculates a reward based on performance metrics (e.g., penalizing high latency or resource waste).

% [段6：强调模块之间的耦合点，体现系统的完整性和灵活性]
This modular design enables the agent to be replaced, reconfigured, or retrained independently of the underlying workload and cluster configuration. Moreover, since the environment is based on standard Kubernetes APIs and open-source components, the framework is portable and extensible to other RL algorithms, model architectures, or deployment platforms.
