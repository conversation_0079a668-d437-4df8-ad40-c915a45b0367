

% [段1：概述实验目标，建立与前文方法的衔接]
We conduct a series of experiments to evaluate the effectiveness of our RL-based resource orchestration framework in improving inference performance under dynamic workloads. Specifically, we compare our approach to the default Kubernetes Horizontal Pod Autoscaler (HPA) using MobileNetV4 inference workloads in a GPU-enabled local Kind cluster.

% [段2：介绍实验环境配置，强调真实性和可复现性]
\subsection{Experimental Setup}
All experiments are conducted on a single machine running Ubuntu 24.04 with an NVIDIA RTX 3080 GPU (8GB VRAM). The Kubernetes cluster is deployed using MicroK8s and configured to support GPU scheduling through the NVIDIA container runtime and GPU Operator. We create logical partitions on the single physical node using labels and node selectors to simulate multi-node scheduling scenarios. The inference workload uses MobileNetV4 models served through NVIDIA Triton Inference Server, with both GPU and CPU variants deployed for comparison.

Our experimental framework includes:
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{GPU Workloads:} 3 replicas of MobileNetV4 Triton servers with GPU acceleration
  \item \textbf{CPU Workloads:} 3 replicas of MobileNetV4 Triton servers using CPU-only inference
  \item \textbf{Memory-Intensive Workloads:} 3 replicas of Redis instances to simulate diverse resource demands
  \item \textbf{Load Generation:} Locust-based load testing with four distinct traffic patterns
\end{itemize}

The load generator implements four synthetic traffic profiles to evaluate scheduling behavior under different conditions:
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{Ramp Pattern:} Gradually increasing load from 14 to 100 users over 300 seconds, simulating steady growth scenarios
  \item \textbf{Spike Pattern:} Sudden bursts from 10 to 100 users with rapid transitions, testing burst handling capabilities
  \item \textbf{Periodic Pattern:} Sinusoidal load variation between 10-100 users with three complete cycles over 300 seconds
  \item \textbf{Random Pattern:} Stochastic load changes between 13-96 users following a uniform distribution, simulating unpredictable traffic
\end{itemize}

Each load pattern is designed to stress different aspects of the scheduling system: ramp patterns test gradual scaling behavior, spike patterns evaluate burst response, periodic patterns assess cyclic load handling, and random patterns challenge adaptive scheduling under uncertainty.

% [段3：定义关键评估指标，建立量化对比基础]
\subsection{Evaluation Metrics}
We evaluate the baseline Kubernetes scheduler performance using comprehensive metrics collected during our experiments:

\textbf{Performance Metrics:}
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{Response Time}: P95 latency for inference requests across different load patterns
  \item \textbf{Throughput}: Successful requests per second under varying load conditions
  \item \textbf{Accuracy}: Model inference accuracy using synthetic test datasets
\end{itemize}

\textbf{Resource Utilization Metrics:}
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{CPU Usage}: Per-pod and node-level CPU consumption
  \item \textbf{Memory Usage}: Memory allocation and utilization patterns
  \item \textbf{GPU Utilization}: GPU memory and compute usage for GPU-enabled pods
\end{itemize}

\textbf{Scheduling Metrics:}
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{Pod Distribution}: Distribution of pods across logical partitions
  \item \textbf{Scheduling Latency}: Time from pod creation to successful scheduling
  \item \textbf{Resource Allocation}: Analysis of resource requests vs. actual usage
\end{itemize}

Each load pattern is executed for 300 seconds (ramp and periodic) or 100 seconds (spike) or 50 seconds (random) with comprehensive metric collection throughout the experiment duration. The varying durations are optimized for each pattern's characteristics while ensuring sufficient data collection for statistical analysis.

% [段4：说明对比方案，突出 RL agent 相较于 HPA 的研究价值]
\subsection{Baseline Evaluation}
In this phase, we establish comprehensive baseline performance metrics for the default Kubernetes scheduler across different workload types:

\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{GPU Baseline}: Performance of 3 GPU-accelerated MobileNetV4 Triton servers under all four load patterns
  \item \textbf{CPU Baseline}: Performance of 3 CPU-only MobileNetV4 Triton servers under identical load conditions
  \item \textbf{Mixed Workload}: Combined deployment of GPU, CPU, and memory-intensive workloads to evaluate scheduling decisions under resource contention
\end{itemize}

The baseline evaluation serves two critical purposes: (1) establishing performance benchmarks for future RL-based improvements, and (2) analyzing the default Kubernetes scheduler's behavior patterns to identify optimization opportunities. All baseline experiments use the default Kubernetes scheduler without any custom scheduling policies or autoscaling mechanisms.

% [段5：描述实验流程，确保实验步骤完整规范]
\subsection{Evaluation Procedure}
Our experimental methodology follows a systematic approach to ensure reproducible and comprehensive baseline data collection:

\begin{enumerate}
  \item \textbf{Environment Preparation}: Create logical partitions using node labels and deploy workloads with appropriate node selectors
  \item \textbf{Workload Deployment}: Deploy 3 replicas each of GPU, CPU, and memory-intensive workloads
  \item \textbf{Load Pattern Execution}: Execute each of the four load patterns (ramp, spike, periodic, random) sequentially
  \item \textbf{Metric Collection}: Continuously collect performance, resource utilization, and scheduling metrics throughout each 300-second test
  \item \textbf{Synthetic Evaluation}: Run synthetic inference tests to measure accuracy and baseline performance
  \item \textbf{Data Analysis}: Generate comprehensive reports and visualizations using custom analysis scripts
\end{enumerate}

The entire experimental suite is automated through a Makefile-based framework that ensures consistent execution and comprehensive data collection. All experiments are conducted on a clean cluster state to eliminate interference between test runs.

% [段6：RL训练实验设计]
\subsection{RL Training Experiments}
Building upon the baseline evaluation, we implement and train a Proximal Policy Optimization (PPO) agent to learn intelligent auto-scaling policies for containerized inference workloads. The RL training experiments are designed to demonstrate adaptive resource management capabilities that improve upon traditional threshold-based auto-scaling approaches.

\textbf{RL Agent Configuration:}
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{Algorithm}: PPO with actor-critic architecture (137,998 parameters)
  \item \textbf{State Space}: 10-dimensional observation vector including resource utilization, latency, throughput, current replica counts, load trends, and episode progress
  \item \textbf{Action Space}: Multi-discrete actions for dynamic replica scaling: GPU replicas (scale down 2/1, maintain, scale up 1/2), CPU replicas (scale down 2/1, maintain, scale up 1/2), and workload placement preference
  \item \textbf{Reward Function}: Multi-objective optimization balancing latency improvement, throughput enhancement, resource efficiency, and scaling stability
\end{itemize}

\textbf{Training Configuration:}
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item \textbf{Episodes}: 100 training episodes with 5 steps each (300 seconds per episode)
  \item \textbf{Load Pattern Rotation}: Cyclic rotation through all four load patterns (ramp, spike, periodic, random)
  \item \textbf{Evaluation Intervals}: Comprehensive evaluation every 20 episodes across all patterns
  \item \textbf{Hardware}: NVIDIA RTX 3080 GPU for accelerated training
\end{itemize}

\textbf{Training Environment:}
The RL agent operates as an intelligent auto-scaling controller, directly manipulating Kubernetes Deployment replica counts through the Kubernetes API. Unlike traditional Horizontal Pod Autoscaler (HPA) that relies on simple CPU/memory thresholds, our agent observes multi-dimensional system state and learns pattern-specific scaling strategies. The agent monitors real-time metrics via Prometheus and executes scaling decisions by patching deployment specifications, implementing a closed-loop control system for adaptive resource management.

% [段7：预告分析章节，将实验数据用于下一节的统计比较]
The collected baseline results and RL training data provide a comprehensive foundation for understanding both default Kubernetes scheduler behavior and the learning capabilities of our proposed RL-based approach. The next section presents detailed analysis of these measurements and demonstrates the performance improvements achieved through reinforcement learning.
