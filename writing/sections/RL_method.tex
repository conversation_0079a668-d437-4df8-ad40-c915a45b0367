
% [段1：本节概述，说明训练目标是让 agent 学习资源调度策略]
Our goal is to train a reinforcement learning agent that learns an effective resource orchestration policy for GPU-accelerated inference workloads. We adopt the Proximal Policy Optimization (PPO) algorithm due to its stability and suitability for continuous control problems. The agent interacts with the Kubernetes cluster as an environment, and its actions directly affect the system's resource allocation and performance.

% [段2：定义state空间——Agent可感知的系统状态变量]
\subsection{State Representation}
At each time step, the agent observes a feature vector that represents the current state of the system. The state vector $s_t \in \mathbb{R}^{10}$ is defined as:
\[
s_t = [n_{\text{replicas}}, u_{\text{GPU}}, l_{\text{p95}}, \theta_{\text{req}}, u_{\text{CPU}}, u_{\text{mem}}, \Delta l, \Delta \theta, t_{\text{norm}}, p_{\text{id}}]
\]
where each component represents:
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item $n_{\text{replicas}}$: Number of running replicas of the inference service
  \item $u_{\text{GPU}}$: GPU utilization (node-level and container-level)
  \item $l_{\text{p95}}$: Request latency (p95)
  \item $\theta_{\text{req}}$: Request throughput
  \item $u_{\text{CPU}}, u_{\text{mem}}$: CPU and memory usage (normalized)
  \item $\Delta l, \Delta \theta$: Latency and throughput trends
  \item $t_{\text{norm}}$: Normalized episode progress
  \item $p_{\text{id}}$: Load pattern identifier
\end{itemize}
All features are normalized to the range [0, 1] before being fed into the agent.

% [段3：定义action空间——Agent可以控制的动作]
\subsection{Action Space}
The agent controls the number of replicas in the inference service deployment. We define a multi-discrete action space $\mathcal{A} = \mathcal{A}_{\text{GPU}} \times \mathcal{A}_{\text{CPU}} \times \mathcal{A}_{\text{pref}}$ where:
\[
\mathcal{A}_{\text{GPU}} = \{-2, -1, 0, +1, +2\}, \quad \mathcal{A}_{\text{CPU}} = \{-2, -1, 0, +1, +2\}, \quad \mathcal{A}_{\text{pref}} = \{0, 1\}
\]
The action components represent:
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item $\mathcal{A}_{\text{GPU}}$: GPU replica scaling decisions (decrease by 2/1, maintain, increase by 1/2)
  \item $\mathcal{A}_{\text{CPU}}$: CPU replica scaling decisions (decrease by 2/1, maintain, increase by 1/2)
  \item $\mathcal{A}_{\text{pref}}$: Workload placement preference (CPU-first or GPU-first)
\end{itemize}
Each action is translated into a Kubernetes API call that patches the deployment's replica field.

% [段4：定义reward函数——训练目标的量化表达]
\subsection{Reward Function}
The reward is designed to balance latency minimization and resource efficiency. Specifically, the reward at time $t$ is defined as:
\[
r_t = -\alpha \cdot \text{Latency}_t + \beta \cdot \text{GPUUtil}_t - \gamma \cdot \text{ReplicaOverhead}_t
\]
where:
\begin{itemize}
  \item $\text{Latency}_t$: measured p95 latency
  \item $\text{GPUUtil}_t$: average GPU utilization
  \item $\text{ReplicaOverhead}_t$: penalty if replicas exceed workload need
  \item $\alpha, \beta, \gamma$: tunable weights to control reward balance
\end{itemize}

% [段5：介绍训练流程——训练循环的运行逻辑]
\subsection{Training Loop}
The training process proceeds in episodes. Each episode spans a fixed number of time steps. At each step, the agent:
\begin{enumerate}
  \item Queries Prometheus for the current system state.
  \item Selects an action based on its policy.
  \item Applies the action via the Kubernetes API.
  \item Waits for a short stabilization period.
  \item Collects performance metrics and computes reward.
  \item Updates the policy using PPO.
\end{enumerate}

% [段6：收敛检测和模型保存]
\subsection{Convergence and Model Saving}
The training continues until convergence is detected based on reward variance or average performance improvement. Once training converges, the learned policy is saved to disk and used in the evaluation phase without further updates.