
% [段1：本节概述，说明训练目标是让 agent 学习资源调度策略]
Our goal is to train a reinforcement learning agent that learns an effective resource orchestration policy for GPU-accelerated inference workloads. We adopt the Proximal Policy Optimization (PPO) algorithm due to its stability and suitability for continuous control problems. The agent interacts with the Kubernetes cluster as an environment, and its actions directly affect the system's resource allocation and performance.

% [段2：定义state空间——Agent可感知的系统状态变量]
\subsection{State Representation}
At each time step, the agent observes a feature vector that represents the current state of the system. The state vector $s_t \in \mathbb{R}^{10}$ is defined as:
\[
s_t = [n_{\text{replicas}}, u_{\text{GPU}}, l_{\text{p95}}, \theta_{\text{req}}, u_{\text{CPU}}, u_{\text{mem}}, \Delta l, \Delta \theta, t_{\text{norm}}, p_{\text{id}}]
\]
where each component represents:
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item $n_{\text{replicas}}$: Number of running replicas of the inference service
  \item $u_{\text{GPU}}$: GPU utilization (node-level and container-level)
  \item $l_{\text{p95}}$: Request latency (p95)
  \item $\theta_{\text{req}}$: Request throughput
  \item $u_{\text{CPU}}, u_{\text{mem}}$: CPU and memory usage (normalized)
  \item $\Delta l, \Delta \theta$: Latency and throughput trends
  \item $t_{\text{norm}}$: Normalized episode progress
  \item $p_{\text{id}}$: Load pattern identifier
\end{itemize}
All features are normalized to the range [0, 1] before being fed into the agent.

% [段3：定义action空间——Agent可以控制的动作]
\subsection{Action Space}
The agent controls the number of replicas in the inference service deployment. We define a multi-discrete action space $\mathcal{A} = \mathcal{A}_{\text{GPU}} \times \mathcal{A}_{\text{CPU}} \times \mathcal{A}_{\text{pref}}$ where:
\begin{align}
\mathcal{A}_{\text{GPU}} &= \{-2, -1, 0, +1, +2\} \\
\mathcal{A}_{\text{CPU}} &= \{-2, -1, 0, +1, +2\} \\
\mathcal{A}_{\text{pref}} &= \{0, 1\}
\end{align}
The action components represent:
\begin{itemize}[label=$\blacksquare$, leftmargin=*]
  \item $\mathcal{A}_{\text{GPU}}$: GPU replica scaling decisions (decrease by 2/1, maintain, increase by 1/2)
  \item $\mathcal{A}_{\text{CPU}}$: CPU replica scaling decisions (decrease by 2/1, maintain, increase by 1/2)
  \item $\mathcal{A}_{\text{pref}}$: Workload placement preference (CPU-first or GPU-first)
\end{itemize}
Each action is translated into a Kubernetes API call that patches the deployment's replica field.

% [段4：定义reward函数——训练目标的量化表达]
\subsection{Reward Function}
The reward is designed to balance latency minimization and resource efficiency. Specifically, the reward at time $t$ is defined as:
\[
r_t = -\alpha \cdot \text{Latency}_t + \beta \cdot \text{GPUUtil}_t - \gamma \cdot \text{ReplicaOverhead}_t
\]
where:
\begin{itemize}
  \item $\text{Latency}_t$: measured p95 latency
  \item $\text{GPUUtil}_t$: average GPU utilization
  \item $\text{ReplicaOverhead}_t$: penalty if replicas exceed workload need
  \item $\alpha, \beta, \gamma$: tunable weights to control reward balance
\end{itemize}

% [段5：介绍训练流程——训练循环的运行逻辑]
\subsection{Training Loop}
The training process proceeds in episodes. Each episode spans a fixed number of time steps. At each step, the agent:
\begin{enumerate}
  \item Queries Prometheus for the current system state.
  \item Selects an action based on its policy.
  \item Applies the action via the Kubernetes API.
  \item Waits for a short stabilization period.
  \item Collects performance metrics and computes reward.
  \item Updates the policy using PPO.
\end{enumerate}

% [段6：算法伪代码]
\subsection{Algorithm Description}

The complete RL-based Kubernetes scheduling algorithm consists of three main components: the training procedure, environment interaction, and reward computation. The following algorithms present the main training loop, environment interaction process, and reward calculation.

\textbf{Algorithm 1: RL-based Kubernetes Scheduling Training}

\noindent\textbf{Input:} Kubernetes cluster $K$, PPO agent $\pi_\theta$, training episodes $N$ \\
\textbf{Output:} Trained policy $\pi_\theta^*$

\begin{enumerate}
\item Initialize PPO agent $\pi_\theta$ with random parameters
\item Initialize experience buffer $\mathcal{B} \leftarrow \emptyset$
\item $\text{best\_reward} \leftarrow -\infty$
\item \textbf{for} episode $e = 1$ to $N$ \textbf{do}
\begin{enumerate}
    \item $s_0 \leftarrow$ ResetEnvironment($K$)
    \item $\text{episode\_reward} \leftarrow 0$, $\text{episode\_buffer} \leftarrow \emptyset$
    \item \textbf{for} step $t = 0$ to $T-1$ \textbf{do}
    \begin{enumerate}
        \item $a_t \leftarrow \pi_\theta(s_t)$ \quad // Sample action from policy
        \item $s_{t+1}, r_t, \text{done} \leftarrow$ EnvironmentStep($K, a_t$)
        \item $\text{episode\_buffer} \leftarrow \text{episode\_buffer} \cup \{(s_t, a_t, r_t, s_{t+1})\}$
        \item $\text{episode\_reward} \leftarrow \text{episode\_reward} + r_t$
        \item $s_t \leftarrow s_{t+1}$
        \item \textbf{if} done \textbf{then} break
    \end{enumerate}
    \item $\mathcal{B} \leftarrow \mathcal{B} \cup \text{episode\_buffer}$
    \item \textbf{if} $|\mathcal{B}| \geq \text{batch\_size}$ \textbf{then}
    \begin{enumerate}
        \item $\theta \leftarrow$ PPOUpdate($\pi_\theta, \mathcal{B}$)
        \item $\mathcal{B} \leftarrow \emptyset$
    \end{enumerate}
    \item \textbf{if} $\text{episode\_reward} > \text{best\_reward}$ \textbf{then}
    \begin{enumerate}
        \item $\text{best\_reward} \leftarrow \text{episode\_reward}$
        \item SaveModel($\pi_\theta$)
    \end{enumerate}
\end{enumerate}
\item \textbf{return} $\pi_\theta$
\end{enumerate}

\textbf{Algorithm 2: Environment Interaction}

\noindent\textbf{Input:} Kubernetes cluster $K$, action $a_t = (a_{\text{GPU}}, a_{\text{CPU}}, a_{\text{pref}})$ \\
\textbf{Output:} Next state $s_{t+1}$, reward $r_t$, termination flag

\begin{enumerate}
\item $\text{current\_replicas} \leftarrow$ GetCurrentReplicas($K$)
\item $\text{gpu\_replicas} \leftarrow \text{current\_replicas}[\text{GPU}] + a_{\text{GPU}}$
\item $\text{cpu\_replicas} \leftarrow \text{current\_replicas}[\text{CPU}] + a_{\text{CPU}}$
\item $\text{gpu\_replicas} \leftarrow \max(0, \min(\text{gpu\_replicas}, \text{MAX\_GPU\_REPLICAS}))$
\item $\text{cpu\_replicas} \leftarrow \max(0, \min(\text{cpu\_replicas}, \text{MAX\_CPU\_REPLICAS}))$
\item UpdateDeployment($K$, GPU, $\text{gpu\_replicas}$)
\item UpdateDeployment($K$, CPU, $\text{cpu\_replicas}$)
\item \textbf{if} $a_{\text{pref}} = 1$ \textbf{then} \quad // GPU-first preference
\begin{enumerate}
    \item SetNodeAffinity($K$, GPU\_PREFERRED)
\end{enumerate}
\textbf{else}
\begin{enumerate}
    \item SetNodeAffinity($K$, CPU\_PREFERRED)
\end{enumerate}
\item Sleep($\text{ACTION\_INTERVAL}$) \quad // Wait for system stabilization
\item $\text{metrics} \leftarrow$ CollectMetrics($K$)
\item $s_{t+1} \leftarrow$ ExtractState($\text{metrics}$)
\item $r_t \leftarrow$ ComputeReward($\text{metrics}$)
\item $\text{done} \leftarrow$ CheckTermination($t$)
\item \textbf{return} $s_{t+1}, r_t, \text{done}$
\end{enumerate}

\textbf{Algorithm 3: Reward Computation}

\noindent\textbf{Input:} System metrics $\text{metrics}$, baseline performance $\text{baseline}$ \\
\textbf{Output:} Reward value $r_t$

\begin{enumerate}
\item $l_{\text{current}} \leftarrow \text{metrics}[\text{latency\_p95}]$
\item $\theta_{\text{current}} \leftarrow \text{metrics}[\text{throughput}]$
\item $u_{\text{gpu}} \leftarrow \text{metrics}[\text{gpu\_utilization}]$
\item $u_{\text{cpu}} \leftarrow \text{metrics}[\text{cpu\_utilization}]$
\item $n_{\text{total}} \leftarrow \text{metrics}[\text{total\_replicas}]$
\item // Latency improvement reward
\item $r_{\text{latency}} \leftarrow \frac{\text{baseline}[\text{latency\_p95}] - l_{\text{current}}}{\text{baseline}[\text{latency\_p95}]}$
\item // Throughput improvement reward
\item $r_{\text{throughput}} \leftarrow \frac{\theta_{\text{current}} - \text{baseline}[\text{throughput}]}{\text{baseline}[\text{throughput}]}$
\item // Resource efficiency reward
\item $r_{\text{efficiency}} \leftarrow \alpha_{\text{gpu}} \cdot u_{\text{gpu}} + \alpha_{\text{cpu}} \cdot u_{\text{cpu}}$
\item // Stability penalty
\item $r_{\text{stability}} \leftarrow -\beta \cdot \max(0, n_{\text{total}} - \text{OPTIMAL\_REPLICAS})$
\item // Combined reward
\item $r_t \leftarrow w_1 \cdot r_{\text{latency}} + w_2 \cdot r_{\text{throughput}} + w_3 \cdot r_{\text{efficiency}} + r_{\text{stability}}$
\item \textbf{return} $r_t$
\end{enumerate}

% [段7：收敛检测和模型保存]
\subsection{Convergence and Model Saving}
The training continues until convergence is detected based on reward variance or average performance improvement. Once training converges, the learned policy is saved to disk and used in the evaluation phase without further updates.