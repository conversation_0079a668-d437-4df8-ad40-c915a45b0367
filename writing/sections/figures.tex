% Figure definitions for the paper

% System Architecture Overview
\begin{figure}[htbp]
\centering
\fbox{\parbox{0.8\textwidth}{\centering
\textbf{System Architecture Overview}\\[0.5em]
\textit{A) Workload Generation:} Ramp, Spike, Periodic, Random patterns\\
\textit{B) Kubernetes Cluster:} Kind with GPU/CPU partitions\\
\textit{C) Monitoring:} Prometheus + DCGM-Exporter + Grafana\\
\textit{D) RL Controller:} PPO Agent (external) via K8s API\\[0.5em]
Hardware: Intel i7 + RTX 3080 + 32GB RAM
}}
\caption{System Architecture Overview. The framework consists of a Kubernetes cluster (deployed via Kind), monitoring infrastructure (Prometheus, DCGM-Exporter), MobileNetV4 inference workloads, and an external RL agent that interacts with the system through Kubernetes and Prometheus APIs.}
\label{fig:system-overview}
\end{figure}

% Comprehensive performance comparison chart
\begin{figure}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{figs/baseline/comprehensive_performance_comparison.png}
\caption{Comprehensive Performance Comparison Between GPU and CPU Baselines Across Load Patterns. The chart shows P95 response times, throughput rates, and speedup factors for all four load patterns, demonstrating GPU advantages under irregular and burst loads.}
\label{fig:comprehensive-comparison}
\end{figure}

% Synthetic test comparison
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/baseline/synthetic_test_comparison.png}
\caption{Synthetic Test Performance Comparison Between GPU and CPU Platforms. Under controlled conditions with single inference requests, both platforms achieve nearly identical performance, indicating that GPU benefits emerge primarily under concurrent load scenarios.}
\label{fig:synthetic-comparison}
\end{figure}

% Load pattern examples - GPU versions
\begin{figure}[htbp]
\centering
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/ramp_20250523_173843_plot.png}
\caption{Ramp Pattern (GPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/spike_20250523_180351_plot.png}
\caption{Spike Pattern (GPU)}
\end{subfigure}
\vskip\baselineskip
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/periodic_20250523_202640_plot.png}
\caption{Periodic Pattern (GPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/random_20250523_184818_plot.png}
\caption{Random Pattern (GPU)}
\end{subfigure}
\caption{Load Pattern Visualization for GPU Baseline Tests. Each subplot shows the user count variation over time using relative time points (t1, t2, etc.) for better readability. The patterns demonstrate precise load generation across different traffic scenarios.}
\label{fig:load-patterns}
\end{figure}

% Load pattern examples - CPU versions for comparison
\begin{figure}[htbp]
\centering
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/ramp_20250523_174854_plot.png}
\caption{Ramp Pattern (CPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/spike_20250523_181339_plot.png}
\caption{Spike Pattern (CPU)}
\end{subfigure}
\vskip\baselineskip
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/periodic_20250523_183319_plot.png}
\caption{Periodic Pattern (CPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/random_20250523_185806_plot.png}
\caption{Random Pattern (CPU)}
\end{subfigure}
\caption{Load Pattern Validation for CPU Baseline Tests. The CPU tests show identical load generation patterns to GPU tests, ensuring fair performance comparison across platforms. All patterns maintain consistent user count variations as designed.}
\label{fig:cpu-load-patterns}
\end{figure}

% RL Training Progress
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/rl/individual/rl_training_progress.png}
\caption{RL Training Progress. Episode rewards with moving average trend showing clear convergence with 11.6\% improvement over 100 episodes.}
\label{fig:rl-training-progress}
\end{figure}

% RL Reward Distribution
\begin{figure}[htbp]
\centering
\includegraphics[width=0.7\textwidth]{figs/rl/individual/rl_reward_distribution.png}
\caption{RL Reward Distribution. Histogram showing the distribution of episode rewards across all training episodes, indicating multi-modal learning behavior.}
\label{fig:rl-reward-distribution}
\end{figure}

% RL Pattern Performance
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/rl/individual/rl_pattern_performance.png}
\caption{RL Performance by Load Pattern. Average reward comparison across different load patterns with error bars showing standard deviation. Periodic patterns achieve highest performance while random patterns present the greatest challenge.}
\label{fig:rl-pattern-performance}
\end{figure}

% RL Learning by Pattern
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/rl/individual/rl_learning_by_pattern.png}
\caption{RL Learning Progress by Pattern. Scatter plot showing episode rewards color-coded by load pattern, demonstrating pattern-specific learning behaviors and adaptation strategies.}
\label{fig:rl-learning-by-pattern}
\end{figure>

% Training Rewards (from training_progress.png)
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/rl/individual/training_rewards.png}
\caption{Training Rewards Progress. Episode rewards with moving average showing the learning curve during training with clear convergence trends.}
\label{fig:training-rewards}
\end{figure}

% Episode Lengths
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/rl/individual/episode_lengths.png}
\caption{Episode Lengths. Duration of each training episode showing consistent episode length throughout training.}
\label{fig:episode-lengths}
\end{figure}

% Training Losses
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/rl/individual/training_losses.png}
\caption{Training Losses. Policy and value function losses during training showing convergence of the learning algorithm.}
\label{fig:training-losses}
\end{figure}

% Pattern Performance from Training
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/rl/individual/pattern_performance_training.png}
\caption{Pattern Performance During Training. Average reward by load pattern during the training phase with error bars showing variability.}
\label{fig:pattern-performance-training}
\end{figure}