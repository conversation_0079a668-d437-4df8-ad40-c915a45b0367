% Figure definitions for the paper

% System Architecture Overview
\begin{figure}[htbp]
\centering
\fbox{\parbox{0.8\textwidth}{\centering
\textbf{System Architecture Overview}\\[0.5em]
\textit{A) Workload Generation:} Ramp, Spike, Periodic, Random patterns\\
\textit{B) Kubernetes Cluster:} Kind with GPU/CPU partitions\\
\textit{C) Monitoring:} Prometheus + DCGM-Exporter + Grafana\\
\textit{D) RL Controller:} PPO Agent (external) via K8s API\\[0.5em]
Hardware: Intel i7 + RTX 3080 + 32GB RAM
}}
\caption{System Architecture Overview. The framework consists of a Kubernetes cluster (deployed via Kind), monitoring infrastructure (Prometheus, DCGM-Exporter), MobileNetV4 inference workloads, and an external RL agent that interacts with the system through Kubernetes and Prometheus APIs.}
\label{fig:system-overview}
\end{figure}

% Comprehensive performance comparison chart
\begin{figure}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{figs/baseline/comprehensive_performance_comparison.png}
\caption{Comprehensive Performance Comparison Between GPU and CPU Baselines Across Load Patterns. The chart shows P95 response times, throughput rates, and speedup factors for all four load patterns, demonstrating GPU advantages under irregular and burst loads.}
\label{fig:comprehensive-comparison}
\end{figure}

% Synthetic test comparison
\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{figs/baseline/synthetic_test_comparison.png}
\caption{Synthetic Test Performance Comparison Between GPU and CPU Platforms. Under controlled conditions with single inference requests, both platforms achieve nearly identical performance, indicating that GPU benefits emerge primarily under concurrent load scenarios.}
\label{fig:synthetic-comparison}
\end{figure}

% Load pattern examples - GPU versions
\begin{figure}[htbp]
\centering
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/ramp_20250523_173843_plot.png}
\caption{Ramp Pattern (GPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/spike_20250523_180351_plot.png}
\caption{Spike Pattern (GPU)}
\end{subfigure}
\vskip\baselineskip
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/periodic_20250523_202640_plot.png}
\caption{Periodic Pattern (GPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/random_20250523_184818_plot.png}
\caption{Random Pattern (GPU)}
\end{subfigure}
\caption{Load Pattern Visualization for GPU Baseline Tests. Each subplot shows the user count variation over time using relative time points (t1, t2, etc.) for better readability. The patterns demonstrate precise load generation across different traffic scenarios.}
\label{fig:load-patterns}
\end{figure}

% Load pattern examples - CPU versions for comparison
\begin{figure}[htbp]
\centering
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/ramp_20250523_174854_plot.png}
\caption{Ramp Pattern (CPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/spike_20250523_181339_plot.png}
\caption{Spike Pattern (CPU)}
\end{subfigure}
\vskip\baselineskip
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/periodic_20250523_183319_plot.png}
\caption{Periodic Pattern (CPU)}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.45\textwidth}
\includegraphics[width=\textwidth]{figs/baseline/random_20250523_185806_plot.png}
\caption{Random Pattern (CPU)}
\end{subfigure}
\caption{Load Pattern Validation for CPU Baseline Tests. The CPU tests show identical load generation patterns to GPU tests, ensuring fair performance comparison across platforms. All patterns maintain consistent user count variations as designed.}
\label{fig:cpu-load-patterns}
\end{figure}

% RL Training Progress and Analysis
\begin{figure}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{figs/rl/training_progress.png}
\caption{RL Training Progress Analysis. The figure shows (a) episode rewards with moving average trend, (b) reward distribution histogram, (c) performance by load pattern, and (d) learning progress with pattern identification. The training demonstrates clear convergence with 11.6\% improvement over 100 episodes.}
\label{fig:rl-training-progress}
\end{figure}

% RL Comprehensive Analysis
\begin{figure}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{figs/rl/rl_analysis.png}
\caption{Comprehensive RL Performance Analysis. The visualization presents (a) training reward progression with moving average, (b) reward distribution across episodes, (c) pattern-specific performance comparison, and (d) episode-by-episode learning with pattern color coding. The results demonstrate successful pattern recognition and adaptive learning.}
\label{fig:rl-analysis}
\end{figure}
