This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023/Debian) (preloaded format=pdflatex 2025.5.28)  28 MAY 2025 15:42
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**main.tex
(./main.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
(./IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count187
\@IEEEtrantmpcountB=\count188
\@IEEEtrantmpcountC=\count189
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 5
03.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count190
\c@subsection=\count191
\c@subsubsection=\count192
\c@paragraph=\count193
\c@IEEEsubequation=\count194
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count195
\c@table=\count196
\@IEEEeqnnumcols=\count197
\@IEEEeqncolcnt=\count198
\@IEEEsubeqnnumrollback=\count199
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count266
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count267
\@IEEEtranrubishbin=\box52
)
** ATTENTION: Overriding command lockouts (line 2).
(/usr/share/texlive/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
))
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count268
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count269
\leftroot@=\count270
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count271
\DOTSCASE@=\count272
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count273
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count274
\dotsspace@=\muskip16
\c@parentequation=\count275
\dspbrk@lvl=\count276
\tag@help=\toks19
\row@=\count277
\column@=\count278
\maxfields@=\count279
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
\c@ALC@unique=\count280
\c@ALC@line=\count281
\c@ALC@rem=\count282
\c@ALC@depth=\count283
\ALC@tlm=\skip54
\algorithmicindent=\skip55
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen173
\Gin@req@width=\dimen174
)
(/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX

(/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(/usr/share/texlive/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count284
)
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count285
)
\@linkdim=\dimen175
\Hy@linkcounter=\count286
\Hy@pagecounter=\count287

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count288

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count289

(/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen176

(/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count290
\Field@Width=\dimen177
\Fld@charsize=\dimen178
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.

(/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count291
\c@Item=\count292
\c@Hfootnote=\count293
)
Package hyperref Info: Driver (autodetected): hpdftex.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-01-20 v7.01h Hyperref driver for pdfTeX

(/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count294
\c@bookmark@seq@number=\count295

(/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip56
)
(/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count296
\float@exts=\toks24
\float@box=\box55
\@float@everytoks=\toks25
\@floatcapt=\box56
)
\@float@every@algorithm=\toks26
\c@algorithm=\count297
)
(/usr/share/texlive/texmf-dist/tex/latex/comment/comment.sty
\CommentStream=\write3

Excluding comment 'comment')
(/usr/share/texlive/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2022/06/20 v1.0f Color table columns (DPC)

(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen179
\ar@mcellbox=\box57
\extrarowheight=\dimen180
\NC@list=\toks27
\extratabsurround=\skip57
\backup@length=\skip58
\ar@cellbox=\box58
)
\everycr=\toks28
\minrowclearance=\skip59
\rownum=\count298
)

LaTeX Warning: Package "xcolor" has already been loaded: ignoring load-time
(LaTeX)        option "xcdraw".

(/usr/share/texlive/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip60
\enit@outerparindent=\dimen181
\enit@toks=\toks29
\enit@inbox=\box59
\enit@count@id=\count299
\enitdp@description=\count300
)
(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count301
\l__pdf_internal_box=\box60
)
No file main.aux.
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 23.
LaTeX Font Info:    ... okay on input line 23.
-- Lines per column: 56 (exact).
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count302
\scratchdimen=\dimen182
\scratchbox=\box61
\nofMPsegments=\count303
\nofMParguments=\count304
\everyMPshowfont=\toks30
\MPscratchCnt=\count305
\MPscratchDim=\dimen183
\MPnumerator=\count306
\makeMPintoPDFobject=\count307
\everyMPtoPDFconversion=\toks31
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package hyperref Info: Link coloring OFF on input line 23.
\@outlinefile=\write4
\openout4 = `main.out'.

LaTeX Font Info:    Calculating math sizes for size <24> on input line 35.
LaTeX Font Info:    Trying to load font information for U+msa on input line 35.


(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 35.


(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(./sections/introduction.tex) (./sections/background_n_motivation.tex [1{/var/l
ib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texlive/texmf-dist/font
s/enc/dvips/base/8r.enc}


]) (./sections/design.tex

LaTeX Warning: Reference `fig:system-overview' on page 2 undefined on input lin
e 2.

) (./sections/RL_method.tex [2]) (./sections/experiments.tex
Underfull \hbox (badness 1424) in paragraph at lines 12--13
[]\OT1/ptm/b/n/10 GPU Work-loads: \OT1/ptm/m/n/10 3 repli-cas of Mo-bileNetV4 T
ri-ton
 []


Underfull \hbox (badness 1533) in paragraph at lines 13--14
[]\OT1/ptm/b/n/10 CPU Work-loads: \OT1/ptm/m/n/10 3 repli-cas of Mo-bileNetV4 T
ri-ton
 []

[3]) (./sections/results.tex

LaTeX Warning: Reference `fig:comprehensive-comparison' on page 4 undefined on 
input line 7.

[4]

LaTeX Warning: Reference `fig:load-patterns' on page 5 undefined on input line 
27.


LaTeX Warning: Reference `fig:scheduling-analysis' on page 5 undefined on input
 line 39.


LaTeX Warning: Reference `fig:rl-training-progress' on page 5 undefined on inpu
t line 84.


LaTeX Warning: Reference `fig:rl-analysis' on page 5 undefined on input line 90
.

LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 9
2.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
) [5]

LaTeX Warning: Reference `tab:rl-baseline-comparison' on page 6 undefined on in
put line 103.

) (./sections/discussion.tex
Underfull \hbox (badness 1515) in paragraph at lines 19--20
[]\OT1/ptm/m/n/10 Our RL train-ing ex-per-i-ments suc-cess-fully demon-strate
 []

[6]) (./sections/conclusion.tex) [7] (./sections/figures.tex

LaTeX Warning: File `figs/system_architecture.png' not found on input line 6.


! Package pdftex.def Error: File `figs/system_architecture.png' not found: usin
g draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.6 ....9\textwidth]{figs/system_architecture.png}
                                                  
? 
! Interruption.
\GenericError  ...                                
                                                  \endgroup 
l.6 ....9\textwidth]{figs/system_architecture.png}
                                                  
? pdflatex -interaction=nonstopmode main.tex
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 6
.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
)
Overfull \hbox (212.39685pt too wide) in paragraph at lines 6--7
 [][] 
 []

<figs/baseline/comprehensive_performance_comparison.png, id=82, 1076.823pt x 85
8.8085pt>
File: figs/baseline/comprehensive_performance_comparison.png Graphic file (type
 png)
<use figs/baseline/comprehensive_performance_comparison.png>
Package pdftex.def Info: figs/baseline/comprehensive_performance_comparison.png
  used on input line 14.
(pdftex.def)             Requested size: 464.39685pt x 370.36812pt.

Overfull \hbox (212.39685pt too wide) in paragraph at lines 14--15
 [][] 
 []

! Interruption.
\IfHookEmptyTF ...if_empty:cF {__hook_toplevel #1}
                                                  {F}\__hook_cs_if_empty:cF ...
l.17 \end{figure}
                 
? ls -la
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
! Interruption.
\Gread@@pdftex ...n@defaultbp \Gin@urx {\csname #1
                                                   width\Gin@attr@hash \endc...
l.22 ...gs/baseline/synthetic_test_comparison.png}
                                                  
? pdflatex test_main.tex
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
H for help, X to quit.
? 
<figs/baseline/synthetic_test_comparison.png, id=84, 860.013pt x 353.1594pt>
File: figs/baseline/synthetic_test_comparison.png Graphic file (type png)
<use figs/baseline/synthetic_test_comparison.png>
Package pdftex.def Info: figs/baseline/synthetic_test_comparison.png  used on i
nput line 22.
(pdftex.def)             Requested size: 412.80157pt x 169.51457pt.

Overfull \hbox (160.80157pt too wide) in paragraph at lines 22--23
 [][] 
 []


! LaTeX Error: Environment subfigure undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.30 \begin{subfigure}
                      [b]{0.45\textwidth}
? cd writing && rm -f main.pdf main.aux main.log main.out && pdflatex main.tex
Type <return> to proceed, S to scroll future error messages,
R to run without stopping, Q to run quietly,
I to insert something, E to edit your file,
1 or ... or 9 to ignore the next 1 to 9 tokens of input,
H for help, X to quit.
? 
! Emergency stop.
 ...                                              
                                                  
l.30 \begin{subfigure}
                      [b]{0.45\textwidth}
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

 
Here is how much of TeX's memory you used:
 12014 strings out of 474222
 188469 string characters out of 5748733
 1947975 words of memory out of 5000000
 34040 multiletter control sequences out of 15000+600000
 595409 words of font info for 113 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,8n,79p,674b,541s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
