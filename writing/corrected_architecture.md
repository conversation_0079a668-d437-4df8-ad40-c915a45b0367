# 修正后的RL训练架构图

## 主要修正点

### 1. RL Agent位置修正
```
┌─────────────────────────────────────────────────────────────┐
│                    External Application                     │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                 RL Training Process                     ││
│  │  ┌─────────────────┐    ┌─────────────────────────────┐ ││
│  │  │   PPO Agent     │    │    Kubernetes RL Env       │ ││
│  │  │ (137,998 params)│◄──►│  • State observation       │ ││
│  │  │ • Actor Network │    │  • Action execution        │ ││
│  │  │ • Critic Network│    │  • Reward calculation      │ ││
│  │  └─────────────────┘    └─────────────────────────────┘ ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                         │
                         ▼ (API Calls)
┌─────────────────────────────────────────────────────────────┐
│                 Kubernetes Control Plane                    │
│  ┌─────────┐  ┌─────────────────┐  ┌─────────────────────┐  │
│  │  etcd   │  │ controller      │  │   Default           │  │
│  │         │  │ manager         │  │   Scheduler         │  │
│  │         │  │ • Deployment    │  │   (Unchanged)       │  │
│  │         │  │ • ReplicaSet    │  │                     │  │
│  │         │  │ • Service       │  │                     │  │
│  └─────────┘  └─────────────────┘  └─────────────────────┘  │
│                         ▲                                   │
│              ┌─────────────────────┐                       │
│              │   Kube API Server   │                       │
│              │                     │                       │
│              └─────────────────────┘                       │
└─────────────────────────────────────────────────────────────┘
```

### 2. 实际Pod状态
```
┌─────────────────────────────────────────────────────────────┐
│                      Worker Node                            │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                  CPU Partition                          ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       ││
│  │  │Triton CPU 1 │ │Triton CPU 2 │ │Triton CPU 3 │       ││
│  │  │  Running    │ │  Running    │ │  Running    │       ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘       ││
│  └─────────────────────────────────────────────────────────┘│
│  ┌─────────────────────────────────────────────────────────┐│
│  │                  GPU Partition                          ││
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       ││
│  │  │Triton GPU 1 │ │Triton GPU 2 │ │Triton GPU 3 │       ││
│  │  │  Running    │ │  Pending    │ │  Pending    │       ││
│  │  │ (RTX 3080)  │ │(No GPU Avail)│ │(No GPU Avail)│      ││
│  │  └─────────────┘ └─────────────┘ └─────────────┘       ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 3. 数据流修正
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Load Patterns  │───▶│  Locust Master  │───▶│   Triton Pods   │
│ • Ramp          │    │  + Workers      │    │ • 1 GPU Running │
│ • Spike         │    │                 │    │ • 3 CPU Running │
│ • Periodic      │    │                 │    │                 │
│ • Random        │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   RL Agent      │◄───│   Prometheus    │◄───│ Performance     │
│ • Observes      │    │ • Node metrics  │    │ Metrics         │
│ • Decides       │    │ • Pod metrics   │    │ • Latency       │
│ • Acts          │    │ • GPU metrics   │    │ • Throughput    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼ (PATCH Deployments)
┌─────────────────┐
│ Kubernetes API  │
│ • Scale GPU     │
│ • Scale CPU     │
│ • Update specs  │
└─────────────────┘
```

### 4. RL Agent的实际操作
```python
# RL Agent执行的实际操作
def _execute_action(self, gpu_action, cpu_action, placement_action):
    # 获取当前Deployment
    gpu_deployment = self.k8s_apps.read_namespaced_deployment(
        name="mobilenetv4-triton-deployment",
        namespace="workloads"
    )
    cpu_deployment = self.k8s_apps.read_namespaced_deployment(
        name="mobilenetv4-triton-cpu-deployment", 
        namespace="workloads"
    )
    
    # 计算新的副本数
    new_gpu_replicas = current_gpu_replicas + gpu_change
    new_cpu_replicas = current_cpu_replicas + cpu_change
    
    # 更新Deployment规格
    gpu_deployment.spec.replicas = new_gpu_replicas
    cpu_deployment.spec.replicas = new_cpu_replicas
    
    # 通过API应用更改
    self.k8s_apps.patch_namespaced_deployment(...)
```

## 关键修正总结

1. **RL Agent位置**: 从Control Plane内部移到外部应用
2. **调度器**: 保持Default Scheduler不变，移除"Updated Scheduler"
3. **Pod状态**: 显示实际的资源约束（1 GPU Running, 2 Pending）
4. **数据流**: 明确RL Agent如何获取监控数据和执行操作
5. **操作对象**: RL Agent操作Deployment，不是直接操作Pod
6. **控制器链**: 显示Deployment Controller → ReplicaSet Controller的内置流程

这样的架构图更准确地反映了我们的实际实现：RL Agent作为外部智能控制器，通过标准Kubernetes API实现自适应auto-scaling。
