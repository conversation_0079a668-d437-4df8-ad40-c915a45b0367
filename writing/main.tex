\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}

\usepackage{hyperref}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{comment}
\usepackage[table,xcdraw]{xcolor}

\usepackage{enumitem}


\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}

\begin{document}

%\title{\textsc{KaRL}: A \underline{R}einforcement \underline{L}earning-Based \underline{A}utoscaling Method for GPU Inference Workloads on \underline{K}ubernetes
%{\footnotesize \textsuperscript{*}Note: Sub-titles are not captured in Xplore and
%should not be used}
\title{KISim: A GPU-Aware \underline{K}ubernetes \underline{I}nference \underline{Sim}ulator with Reinforcement Learning-Based Autoscaling}
%\thanks{Identify applicable funding agency here. If none, delete this.}
%}

\author{Anonymous}

\maketitle

\begin{abstract}
As deep learning applications increasingly rely on GPU-accelerated inference, efficiently managing computational resources becomes critical in containerized environments such as Kubernetes. While the Horizontal Pod Autoscaler (HPA) offers basic autoscaling functionality, its reactive and threshold-based nature struggles to meet the performance demands of latency-sensitive workloads, especially those utilizing GPUs.

In this work, we propose a reinforcement learning (RL)-based resource orchestration framework that replaces static autoscaling with an adaptive policy learned through interaction with the system. We implement a Proximal Policy Optimization (PPO) agent that dynamically scales inference replicas in a real Kubernetes cluster deployed via Kind on a local Ubuntu system with an NVIDIA RTX 3080 GPU.

Our RL agent observes system metrics via Prometheus, takes control actions via the Kubernetes API, and learns to optimize latency, throughput, and resource utilization. Compared to HPA, the RL-based approach achieves lower p95 latency, higher GPU utilization, and fewer QoS violations across varying workloads. Our framework is fully reproducible, extensible, and bridges the gap between simulation-driven scheduling research and real-system deployment.

This study demonstrates the potential of intelligent orchestration strategies for improving performance in GPU-based Kubernetes environments and opens avenues for future edge and multi-node deployment scenarios.
\end{abstract}


\begin{IEEEkeywords}
Kubernetes, GPU scheduling, reinforcement learning, resource orchestration, system performance evaluation
\end{IEEEkeywords}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section Introduction                             %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Introduction}
\label{sec:introduction}
\input{sections/introduction}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section Background and Motivation                       %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Background and Motivation}
\label{sec:background_motivation}
\input{sections/background_n_motivation}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section System Design                       %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{System Design}
\label{sec:system_design}
\input{sections/design}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section Reinforcement Learning methodology              %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{RL Training Methodology}
\label{RL_method}
\input{sections/RL_method}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section Experiments                                     %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Experiments}
\label{Section/experiments}
\input{sections/experiments}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section Results and Analysis                            %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Results and Analysis}
\label{section/resultsandanalysis}
\input{sections/results}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section Discussion and Future work                      %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Discussion and Future Work}
\label{section/discussion}
\input{sections/discussion}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Section Conclusion                          %%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Conclusion}
\label{section/conclusion}
\input{sections/conclusion}

% Include figures
\input{sections/figures}

% Include tables
\input{tables/baseline_results}
\input{tables/rl_baseline_comparison}

%\section*{Acknowledgment}

%The preferred spelling of the word ``acknowledgment'' in America is without
%an ``e'' after the ``g''. Avoid the stilted expression ``one of us (R. B.
%G.) thanks $\ldots$''. Instead, try ``R. B. G. thanks$\ldots$''. Put sponsor
%acknowledgments in the unnumbered footnote on the first page.

%\section*{References}

\clearpage

\bibliographystyle{IEEEtran}
\bibliography{reference}


\end{document}
