@inproceedings{lane2015can,
  title={Can deep learning revolutionize mobile sensing?},
  author=<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},
  booktitle={Proceedings of the 16th international workshop on mobile computing systems and applications},
  pages={117--122},
  year={2015}
}

@inproceedings{geyer2017diff,
  title={Differentially Private Federated Learning: A Client Level Perspective.},
  author={<PERSON><PERSON>, <PERSON>& <PERSON>, <PERSON> \& <PERSON>, Moin.},
  booktitle={arXiv:1712.07557},
  pages={117--122},
  year={2015}
}

@inproceedings{lin2024efficient,
  title={Efficient Parallel Split Learning over Resource-constrained Wireless Edge Networks.},
  author={<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>},
  journal={IEEE Transactions on Mobile Computing},
  pages={9224-9239},
  year={2024}
}

@inproceedings{Yao2024survey,
  title={A Survey on the Use of Partitioning in IoT-Edge-AI Applications.},
  author={<PERSON> and <PERSON>.},
  journal={arXiv:2406.00301v1},
  year={2024}
}

@inproceedings{kim2023resource,
  title={Partition Placement and Resource Allocation for Multiple DNN-Based Applications in Heterogeneous IoT Environments.},
  author={<PERSON>, Park, Jin, -S. Lee and Lee.},
  journal={IEEE Internet of Things Journal.},
  pages={9836-9848},
  year={2023}
}

@inproceedings{fan2023joint,
  title={Joint DNN Partition and Resource Allocation for Task Offloading in Edge–Cloud-Assisted IoT Environments.},
  author={Fan, Gao, Su, Wu and Liu.},
  journal={IEEE Internet of Things Journal.},
  pages={10146-10159},
  year={2023}
}

@article{cheng2017survey,
  title={A survey of model compression and acceleration for deep neural networks},
  author={Cheng, Yu and Wang, Duo and Zhou, Pan and Zhang, Tao},
  journal={arXiv preprint arXiv:1710.09282},
  year={2017}
}

@inproceedings{dey2019embedded,
  title={Embedded deep inference in practice: Case for model partitioning},
  author={Dey, Swarnava and Mukherjee, Arijit and Pal, Arpan and P, Balamuralidhar},
  booktitle={Proceedings of the 1st Workshop on Machine Learning on Edge in Sensor Systems},
  pages={25--30},
  year={2019}
}

@article{meng2019efficient,
  title={Efficient winograd convolution via integer arithmetic},
  author={Meng, Lingchuan and Brothers, John},
  journal={arXiv preprint arXiv:1901.01965},
  year={2019}
}

@article{krizhevsky2017imagenet,
  title={ImageNet classification with deep convolutional neural networks},
  author={Krizhevsky, Alex and Sutskever, Ilya and Hinton, Geoffrey E},
  journal={Communications of the ACM},
  volume={60},
  number={6},
  pages={84--90},
  year={2017},
  publisher={AcM New York, NY, USA}
}

@article{venieris2018deploying,
  title={Deploying deep neural networks in the embedded space},
  author={Venieris, Stylianos I and Kouris, Alexandros and Bouganis, Christos-Savvas},
  journal={arXiv preprint arXiv:1806.08616},
  year={2018}
}

@article{cheng2018recent,
  title={Recent advances in efficient computation of deep convolutional neural networks},
  author={Cheng, Jian and Wang, Pei-song and Li, Gang and Hu, Qing-hao and Lu, Han-qing},
  journal={Frontiers of Information Technology \& Electronic Engineering},
  volume={19},
  pages={64--77},
  year={2018},
  publisher={Springer}
}

@inproceedings{gao2017tetris,
  title={Tetris: Scalable and efficient neural network acceleration with 3d memory},
  author={Gao, Mingyu and Pu, Jing and Yang, Xuan and Horowitz, Mark and Kozyrakis, Christos},
  booktitle={Proceedings of the Twenty-Second International Conference on Architectural Support for Programming Languages and Operating Systems},
  pages={751--764},
  year={2017}
}

@article{guo2019dl,
  title={[DL] A survey of FPGA-based neural network inference accelerators},
  author={Guo, Kaiyuan and Zeng, Shulin and Yu, Jincheng and Wang, Yu and Yang, Huazhong},
  journal={ACM Transactions on Reconfigurable Technology and Systems (TRETS)},
  volume={12},
  number={1},
  pages={1--26},
  year={2019},
  publisher={ACM New York, NY, USA}
}

@article{sze2017efficient,
  title={Efficient processing of deep neural networks: A tutorial and survey},
  author={Sze, Vivienne and Chen, Yu-Hsin and Yang, Tien-Ju and Emer, Joel S},
  journal={Proceedings of the IEEE},
  volume={105},
  number={12},
  pages={2295--2329},
  year={2017},
  publisher={Ieee}
}

@article{wang2018survey,
  title={A survey of FPGA based deep learning accelerators: Challenges and opportunities},
  author={Wang, Teng and Wang, Chao and Zhou, Xuehai and Chen, Huaping},
  journal={arXiv preprint arXiv:1901.04988},
  year={2018}
}

@inproceedings{dey2019offloaded,
  title={Offloaded execution of deep learning inference at edge: Challenges and insights},
  author={Dey, Swarnava and Mondal, Jayeeta and Mukherjee, Arijit},
  booktitle={2019 IEEE International Conference on Pervasive Computing and Communications Workshops (PerCom Workshops)},
  pages={855--861},
  year={2019},
  organization={IEEE}
}

@inproceedings{dey2018implementing,
  title={Implementing deep learning and inferencing on fog and edge computing systems},
  author={Dey, Swarnava and Mukherjee, Arijit},
  booktitle={2018 IEEE International Conference on Pervasive Computing and Communications Workshops (PerCom Workshops)},
  pages={818--823},
  year={2018},
  organization={IEEE}
}

@inproceedings{dey2018partitioning,
  title={Partitioning of cnn models for execution on fog devices},
  author={Dey, Swarnava and Mukherjee, Arijit and Pal, Arpan and Balamuralidhar, P},
  booktitle={Proceedings of the 1st ACM international workshop on smart cities and fog computing},
  pages={19--24},
  year={2018}
}

@article{kang2017neurosurgeon,
  title={Neurosurgeon: Collaborative intelligence between the cloud and mobile edge},
  author={Kang, Yiping and Hauswald, Johann and Gao, Cao and Rovinski, Austin and Mudge, Trevor and Mars, Jason and Tang, Lingjia},
  journal={ACM SIGARCH Computer Architecture News},
  volume={45},
  number={1},
  pages={615--629},
  year={2017},
  publisher={ACM New York, NY, USA}
}

@inproceedings{teerapittayanon2017distributed,
  title={Distributed deep neural networks over the cloud, the edge and end devices},
  author={Teerapittayanon, Surat and McDanel, Bradley and Kung, Hsiang-Tsung},
  booktitle={2017 IEEE 37th international conference on distributed computing systems (ICDCS)},
  pages={328--339},
  year={2017},
  organization={IEEE}
}

@inproceedings{sandler2018mobilenetv2,
  author    = {Mark Sandler and Andrew Howard and Menglong Zhu and Andrey Zhmoginov and Liang-Chieh Chen},
  title     = {MobileNetV2: Inverted Residuals and Linear Bottlenecks},
  booktitle = {Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)},
  year      = {2018},
  pages     = {4510--4520},
  doi       = {10.1109/CVPR.2018.00474}
}

@article{abouaomar2021resource,
  title={Resource provisioning in edge computing for latency-sensitive applications},
  author={Abouaomar, Amine and Cherkaoui, Soumaya and Mlika, Zoubeir and Kobbane, Abdellatif},
  journal={IEEE Internet of Things Journal},
  volume={8},
  number={14},
  pages={11088--11099},
  year={2021},
  publisher={IEEE}
}

@article{li2019edge,
  title={Edge AI: On-demand accelerating deep neural network inference via edge computing},
  author={Li, En and Zeng, Liekang and Zhou, Zhi and Chen, Xu},
  journal={IEEE Transactions on Wireless Communications},
  volume={19},
  number={1},
  pages={447--457},
  year={2019},
  publisher={IEEE}
}

@inproceedings{bhattacharya2016sparsification,
  title={Sparsification and separation of deep learning layers for constrained resource inference on wearables},
  author={Bhattacharya, Sourav and Lane, Nicholas D},
  booktitle={Proceedings of the 14th ACM Conference on Embedded Network Sensor Systems CD-ROM},
  pages={176--189},
  year={2016}
}

@article{bhardwaj2019memory,
  title={Memory-and communication-aware model compression for distributed deep learning inference on IoT},
  author={Bhardwaj, Kartikeya and Lin, Ching-Yi and Sartor, Anderson and Marculescu, Radu},
  journal={ACM Transactions on Embedded Computing Systems (TECS)},
  volume={18},
  number={5s},
  pages={1--22},
  year={2019},
  publisher={ACM New York, NY, USA}
}

@inproceedings{sun2022cross,
  title={Cross-Stage Fusion Network Based Multi-modal Hyperspectral Image Classification},
  author={Sun, Yuegong and Wang, Zhening and Li, Ao and Jiang, Hailong},
  booktitle={International Conference on 5G for Future Wireless Networks},
  pages={77--88},
  year={2022},
  organization={Springer}
}

@inproceedings{li2020discriminative,
  title={Discriminative subspace learning for cross-view classification with simultaneous local and global alignment},
  author={Li, Ao and Ding, Yu and Chen, Deyun and Sun, Guanglu and Jiang, Hailong},
  booktitle={Neural Computing for Advanced Applications: First International Conference, NCAA 2020, Shenzhen, China, July 3--5, 2020, Proceedings 1},
  pages={168--179},
  year={2020},
  organization={Springer}
}

@article{li2020semi,
  title={Semi-supervised subspace learning for pattern classification via robust low rank constraint},
  author={Li, Ao and An, Ruoqi and Chen, Deyun and Sun, Guanglu and Liu, Xin and Wu, Qidi and Jiang, Hailong},
  journal={Mobile Networks and Applications},
  volume={25},
  pages={2258--2269},
  year={2020},
  publisher={Springer}
}

@article{li2020cross,
  title={Cross-view feature learning via structures unlocking based on robust low-rank constraint},
  author={Li, Ao and Ding, Yu and Chen, Deyun and Sun, Guanglu and Jiang, Hailong and Wu, Qidi},
  journal={IEEE Access},
  volume={8},
  pages={46851--46860},
  year={2020},
  publisher={IEEE}
}