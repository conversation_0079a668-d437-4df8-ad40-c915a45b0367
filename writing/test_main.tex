\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts

\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{comment}
\usepackage[table,xcdraw]{xcolor}
\usepackage{enumitem}

\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}

\begin{document}

\title{KISim: A GPU-Aware \underline{K}ubernetes \underline{I}nference \underline{Sim}ulator with Reinforcement Learning-Based Autoscaling}

\author{Anonymous}

\maketitle

\begin{abstract}
As deep learning applications increasingly rely on GPU-accelerated inference, efficiently managing computational resources becomes critical in containerized environments such as Kubernetes. While the Horizontal Pod Autoscaler (HPA) offers basic autoscaling functionality, its reactive and threshold-based nature struggles to meet the performance demands of latency-sensitive workloads, especially those utilizing GPUs.

In this work, we propose a reinforcement learning (RL)-based resource orchestration framework that replaces static autoscaling with an adaptive policy learned through interaction with the system. We implement a Proximal Policy Optimization (PPO) agent that dynamically scales inference replicas in a real Kubernetes cluster deployed via Kind on a local Ubuntu system with an NVIDIA RTX 3080 GPU.

Our RL agent observes system metrics via Prometheus, takes control actions via the Kubernetes API, and learns to optimize latency, throughput, and resource utilization. Compared to HPA, the RL-based approach achieves lower p95 latency, higher GPU utilization, and fewer QoS violations across varying workloads. Our framework is fully reproducible, extensible, and bridges the gap between simulation-driven scheduling research and real-system deployment.

This study demonstrates the potential of intelligent orchestration strategies for improving performance in GPU-based Kubernetes environments and opens avenues for future edge and multi-node deployment scenarios.
\end{abstract}

\begin{IEEEkeywords}
Kubernetes, GPU scheduling, reinforcement learning, resource orchestration, system performance evaluation
\end{IEEEkeywords}

\section{Introduction}
\label{sec:introduction}

The increasing demand for low-latency inference in AI applications has led to the widespread deployment of deep learning models on GPU-enabled containerized platforms. Kubernetes, as a de facto standard for container orchestration, provides mechanisms such as the Horizontal Pod Autoscaler (HPA) to dynamically scale services based on system metrics like CPU and memory utilization. However, when it comes to GPU-based inference workloads, the default autoscaling strategy often fails to capture the true resource bottlenecks, leading to either under-utilization or unnecessary over-provisioning.

This challenge is exacerbated in edge-like environments with limited GPU resources, where efficient utilization is critical. The static nature of threshold-based HPA does not adapt well to fluctuating workloads, especially for latency-sensitive services like MobileNetV4 inference. Moreover, these autoscaling decisions are typically agnostic to end-to-end application performance such as request latency or quality-of-service (QoS) violations.

To address these limitations, we explore the use of reinforcement learning (RL) to replace traditional autoscaling mechanisms with an adaptive agent that learns to orchestrate GPU resources based on observed system-level and application-level feedback. By formulating the autoscaling problem as a sequential decision-making process, an RL agent can continuously observe system states, apply control actions (e.g., scaling replica count), and receive reward signals based on latency and resource efficiency.

In this work, we build a fully local yet production-like experimental environment leveraging Ubuntu 24.04, NVIDIA RTX 3080, and Kind (Kubernetes-in-Docker). This setup enables realistic GPU utilization, Prometheus-based monitoring, and flexible workload deployment without relying on simulation. Our framework supports both baseline evaluations using standard HPA policies and RL agent training and testing cycles with full observability.

We make the following contributions in this paper:
\begin{itemize} [label=$\blacksquare$, leftmargin=*]
  \item We design and implement an end-to-end RL-based resource orchestration framework for GPU-enabled Kubernetes inference services.
  \item We construct a realistic local environment using real GPU hardware and Kind, enabling accurate and reproducible performance evaluation.
  \item We conduct extensive experiments comparing our RL-based approach with traditional HPA across multiple load profiles, demonstrating improved latency and resource utilization.
  \item We analyze the trade-offs, limitations, and potential for extending our method to real-world edge clusters.
\end{itemize}

\section{Conclusion}

This paper presents KISim, a comprehensive framework for evaluating RL-based autoscaling in GPU-enabled Kubernetes environments. Our experimental results demonstrate the potential of intelligent resource orchestration strategies to improve system performance compared to traditional threshold-based approaches.

\bibliographystyle{IEEEtran}
\bibliography{reference}

\end{document}
