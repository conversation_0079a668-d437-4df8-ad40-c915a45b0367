#!/usr/bin/env python3
import json
import os
import matplotlib.pyplot as plt
import numpy as np

# Load results
with open('results/baseline/metrics/synthetic_results.json', 'r') as f:
    gpu_results = json.load(f)
with open('results/cpu_baseline/metrics/synthetic_results.json', 'r') as f:
    cpu_results = json.load(f)

# Extract metrics
gpu_latency_avg = gpu_results['avg_latency_ms']
gpu_latency_p95 = gpu_results['p95_latency_ms']
gpu_throughput = gpu_results['samples_per_second']

cpu_latency_avg = cpu_results['avg_latency_ms']
cpu_latency_p95 = cpu_results['p95_latency_ms']
cpu_throughput = cpu_results['samples_per_second']

# Calculate speedups
latency_speedup = cpu_latency_avg / gpu_latency_avg if gpu_latency_avg > 0 else float('inf')
throughput_speedup = gpu_throughput / cpu_throughput if cpu_throughput > 0 else float('inf')

# Generate text report
report = f'CPU vs GPU Performance Comparison\n'
report += f'=================================\n'
report += f'CPU Average Latency: {cpu_latency_avg:.2f} ms\n'
report += f'GPU Average Latency: {gpu_latency_avg:.2f} ms\n'
report += f'Latency Speedup (CPU/GPU): {latency_speedup:.2f}x\n\n'
report += f'CPU P95 Latency: {cpu_latency_p95:.2f} ms\n'
report += f'GPU P95 Latency: {gpu_latency_p95:.2f} ms\n\n'
report += f'CPU Throughput: {cpu_throughput:.2f} images/sec\n'
report += f'GPU Throughput: {gpu_throughput:.2f} images/sec\n'
report += f'Throughput Speedup (GPU/CPU): {throughput_speedup:.2f}x\n'

# Save report
with open('results/comparison_report.txt', 'w') as f:
    f.write(report)

print(report)

# Create bar charts
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# Latency comparison
platforms = ['CPU', 'GPU']
avg_latencies = [cpu_latency_avg, gpu_latency_avg]
p95_latencies = [cpu_latency_p95, gpu_latency_p95]

x = np.arange(len(platforms))
width = 0.35

ax1.bar(x - width/2, avg_latencies, width, label='Avg Latency')
ax1.bar(x + width/2, p95_latencies, width, label='P95 Latency')
ax1.set_ylabel('Latency (ms)')
ax1.set_title('Latency Comparison')
ax1.set_xticks(x)
ax1.set_xticklabels(platforms)
ax1.legend()

# Throughput comparison
throughputs = [cpu_throughput, gpu_throughput]
ax2.bar(platforms, throughputs)
ax2.set_ylabel('Throughput (images/sec)')
ax2.set_title('Throughput Comparison')

plt.tight_layout()
plt.savefig('results/comparison_chart.png')
