# RL-based Kubernetes Resource Management: Experimental Architecture

This document provides comprehensive diagrams of the experimental setup for academic publication.

## 1. Overall System Architecture

```mermaid
graph TB
    subgraph "Physical Infrastructure"
        HW[Hardware: Intel i7 + RTX 3080 + 32GB RAM]
    end

    subgraph "Kubernetes Cluster (MicroK8s)"
        subgraph "Control Plane"
            API[Kubernetes API Server]
            SCHED[Default Scheduler]
            CTRL[Controllers]
        end

        subgraph "Worker Node"
            subgraph "GPU Partition"
                GPU_POD1[Triton GPU Pod 1]
                GPU_POD2[Triton GPU Pod 2 - Pending]
                GPU_POD3[Triton GPU Pod 3 - Pending]
            end

            subgraph "CPU Partition"
                CPU_POD1[Triton CPU Pod 1]
                CPU_POD2[Triton CPU Pod 2]
                CPU_POD3[Triton CPU Pod 3]
            end

            subgraph "Load Generation"
                LOCUST_M[Locust Master]
                LOCUST_W1[Locust Worker 1]
                LOCUST_W2[Locust Worker 2]
            end
        end

        subgraph "Monitoring Stack"
            PROM[Prometheus]
            GRAF[Grafana]
            DCGM[DCGM Exporter]
        end
    end

    subgraph "RL Training Environment"
        RL_AGENT[PPO Agent<br/>137,998 parameters]
        RL_ENV[Kubernetes RL Environment]
        RL_CTRL[RL Load Controller]
    end

    subgraph "Experimental Data"
        BASELINE[Baseline Results<br/>4 Load Patterns]
        RL_RESULTS[RL Training Results<br/>100 Episodes]
        COMPARISON[Performance Comparison]
    end

    HW --> API
    API --> SCHED
    SCHED --> GPU_POD1
    SCHED -.-> GPU_POD2
    SCHED -.-> GPU_POD3
    SCHED --> CPU_POD1
    SCHED --> CPU_POD2
    SCHED --> CPU_POD3

    LOCUST_M --> LOCUST_W1
    LOCUST_M --> LOCUST_W2
    LOCUST_W1 --> GPU_POD1
    LOCUST_W1 --> CPU_POD1
    LOCUST_W2 --> CPU_POD2

    PROM --> DCGM
    PROM --> GPU_POD1
    PROM --> CPU_POD1

    RL_ENV --> API
    RL_CTRL --> LOCUST_M
    RL_AGENT --> RL_ENV

    GPU_POD1 --> BASELINE
    CPU_POD1 --> BASELINE
    RL_AGENT --> RL_RESULTS
    BASELINE --> COMPARISON
    RL_RESULTS --> COMPARISON
```

## 2. Data Flow and Processing Pipeline

```mermaid
flowchart TD
    subgraph "Input Layer"
        IMG[MobileNetV4 ONNX Model<br/>224x224 RGB Images]
        LOAD[Dynamic Load Patterns<br/>Ramp, Spike, Periodic, Random]
    end

    subgraph "Load Generation Layer"
        LOCUST[Locust Load Generator]
        PATTERNS[Load Pattern Controller]

        PATTERNS --> LOCUST
        LOAD --> PATTERNS
    end

    subgraph "Inference Layer"
        subgraph "GPU Path"
            TRITON_GPU[Triton Inference Server<br/>GPU Backend]
            GPU_PROC[CUDA Processing<br/>RTX 3080]
        end

        subgraph "CPU Path"
            TRITON_CPU[Triton Inference Server<br/>CPU Backend]
            CPU_PROC[CPU Processing<br/>Intel i7]
        end

        LOCUST --> TRITON_GPU
        LOCUST --> TRITON_CPU
        TRITON_GPU --> GPU_PROC
        TRITON_CPU --> CPU_PROC
        IMG --> TRITON_GPU
        IMG --> TRITON_CPU
    end

    subgraph "Metrics Collection Layer"
        PERF[Performance Metrics<br/>Latency, Throughput, Accuracy]
        RESOURCE[Resource Metrics<br/>CPU, Memory, GPU Utilization]
        QOS[QoS Metrics<br/>P95, P99, Success Rate]

        GPU_PROC --> PERF
        CPU_PROC --> PERF
        GPU_PROC --> RESOURCE
        CPU_PROC --> RESOURCE
        TRITON_GPU --> QOS
        TRITON_CPU --> QOS
    end

    subgraph "Analysis Layer"
        BASELINE_ANALYSIS[Baseline Performance Analysis]
        RL_TRAINING[RL Agent Training & Evaluation]
        COMPARISON_ANALYSIS[Comparative Performance Analysis]

        PERF --> BASELINE_ANALYSIS
        RESOURCE --> BASELINE_ANALYSIS
        QOS --> BASELINE_ANALYSIS

        BASELINE_ANALYSIS --> RL_TRAINING
        RL_TRAINING --> COMPARISON_ANALYSIS
    end

    subgraph "Output Layer"
        RESULTS[Academic Results<br/>Performance Improvements<br/>Resource Efficiency<br/>Scheduling Insights]

        COMPARISON_ANALYSIS --> RESULTS
    end
```

## 3. RL Agent Architecture and Training Process

```mermaid
graph TB
    subgraph "RL Environment (Kubernetes)"
        subgraph "State Space (10D)"
            S1[CPU Utilization %]
            S2[Memory Utilization %]
            S3[GPU Utilization %]
            S4[P95 Latency ms]
            S5[Throughput req/s]
            S6[GPU Replicas Count]
            S7[CPU Replicas Count]
            S8[Load Trend -1 to 1]
            S9[Load Variance 0-1]
            S10[Episode Progress 0-1]
        end

        subgraph "Action Space (Multi-Discrete)"
            A1[GPU Scaling<br/>-2, -1, 0, +1, +2]
            A2[CPU Scaling<br/>-2, -1, 0, +1, +2]
            A3[Workload Placement<br/>GPU, Balanced, CPU]
        end

        subgraph "Reward Function"
            R1[Latency Improvement × 0.4]
            R2[Throughput Improvement × 0.3]
            R3[Resource Efficiency × 0.2]
            R4[Stability Bonus × 0.1]
            TOTAL_R[Total Reward]

            R1 --> TOTAL_R
            R2 --> TOTAL_R
            R3 --> TOTAL_R
            R4 --> TOTAL_R
        end
    end

    subgraph "PPO Agent (137,998 parameters)"
        subgraph "Actor Network"
            ACTOR_IN[Input Layer: 10D]
            ACTOR_H1[Hidden Layer 1: 256]
            ACTOR_H2[Hidden Layer 2: 256]
            ACTOR_H3[Hidden Layer 3: 256]
            ACTOR_OUT[Output Layer: Multi-Discrete]

            ACTOR_IN --> ACTOR_H1
            ACTOR_H1 --> ACTOR_H2
            ACTOR_H2 --> ACTOR_H3
            ACTOR_H3 --> ACTOR_OUT
        end

        subgraph "Critic Network"
            CRITIC_IN[Input Layer: 10D]
            CRITIC_H1[Hidden Layer 1: 256]
            CRITIC_H2[Hidden Layer 2: 256]
            CRITIC_H3[Hidden Layer 3: 256]
            CRITIC_OUT[Value Output: 1D]

            CRITIC_IN --> CRITIC_H1
            CRITIC_H1 --> CRITIC_H2
            CRITIC_H2 --> CRITIC_H3
            CRITIC_H3 --> CRITIC_OUT
        end

        subgraph "Training Parameters"
            LR[Learning Rate: 3e-4]
            GAMMA[Discount Factor: 0.99]
            GAE[GAE Lambda: 0.95]
            CLIP[Clip Epsilon: 0.2]
            ENTROPY[Entropy Coef: 0.01]
        end
    end

    subgraph "Training Loop (100 Episodes)"
        EPISODE[Episode Start]
        PATTERN[Load Pattern Selection<br/>Ramp → Spike → Periodic → Random]
        OBSERVE[Observe State]
        ACTION[Select Action]
        EXECUTE[Execute on Kubernetes]
        REWARD[Calculate Reward]
        UPDATE[Update Policy]
        SAVE[Save Model Checkpoint]

        EPISODE --> PATTERN
        PATTERN --> OBSERVE
        OBSERVE --> ACTION
        ACTION --> EXECUTE
        EXECUTE --> REWARD
        REWARD --> UPDATE
        UPDATE --> SAVE
        SAVE --> EPISODE
    end

    S1 --> ACTOR_IN
    S1 --> CRITIC_IN
    ACTOR_OUT --> A1
    ACTOR_OUT --> A2
    ACTOR_OUT --> A3
    TOTAL_R --> UPDATE

    OBSERVE --> S1
    ACTION --> A1
    EXECUTE --> TOTAL_R
```

## 4. Experimental Methodology and Metrics

```mermaid
flowchart LR
    subgraph "Phase 1: Baseline Experiments"
        B1[Deploy GPU Triton Servers<br/>3 Pods Requested<br/>1 Pod Running]
        B2[Deploy CPU Triton Servers<br/>3 Pods Running]
        B3[Execute Load Patterns<br/>Ramp, Spike, Periodic, Random]
        B4[Collect Baseline Metrics<br/>Latency, Throughput, Resource Usage]

        B1 --> B3
        B2 --> B3
        B3 --> B4
    end

    subgraph "Phase 2: RL Training"
        R1[Initialize RL Environment<br/>State/Action/Reward Design]
        R2[Train PPO Agent<br/>100 Episodes × 4 Load Patterns]
        R3[Model Checkpointing<br/>Every 10 Episodes]
        R4[Evaluation<br/>Every 20 Episodes]

        R1 --> R2
        R2 --> R3
        R2 --> R4
    end

    subgraph "Phase 3: Comparative Analysis"
        C1[RL Agent Evaluation<br/>5 Runs per Load Pattern]
        C2[Statistical Analysis<br/>Mean, Std, Min, Max]
        C3[Performance Comparison<br/>RL vs GPU vs CPU Baselines]
        C4[Academic Results<br/>Publication-Ready Analysis]

        C1 --> C2
        C2 --> C3
        C3 --> C4
    end

    subgraph "Evaluation Metrics"
        subgraph "Performance Metrics"
            P1[P95 Latency ms]
            P2[Throughput req/s]
            P3[Success Rate %]
            P4[Model Accuracy %]
        end

        subgraph "Resource Metrics"
            RS1[CPU Utilization %]
            RS2[Memory Usage MB]
            RS3[GPU Utilization %]
            RS4[GPU Memory MB]
        end

        subgraph "Efficiency Metrics"
            E1[Latency Improvement Factor]
            E2[Throughput Improvement Factor]
            E3[Resource Efficiency Score]
            E4[Cost-Performance Ratio]
        end

        subgraph "Stability Metrics"
            ST1[Scaling Frequency]
            ST2[QoS Violation Rate]
            ST3[Response Time Variance]
            ST4[System Stability Score]
        end
    end

    B4 --> R1
    R4 --> C1

    B4 --> P1
    R4 --> P1
    C1 --> P1

    P1 --> E1
    RS1 --> E3
    E1 --> ST4

## 5. Load Pattern Characteristics and Scheduling Challenges

```mermaid
graph TD
    subgraph "Load Pattern Analysis"
        subgraph "Ramp Pattern"
            RAMP_DESC[Gradual increase<br/>1→100 users over 300s]
            RAMP_CHAR[Characteristics:<br/>• Predictable growth<br/>• Sustained load<br/>• Resource planning opportunity]
            RAMP_CHALLENGE[Scheduling Challenge:<br/>• Proactive scaling<br/>• Resource pre-allocation<br/>• Minimize cold starts]

            RAMP_DESC --> RAMP_CHAR
            RAMP_CHAR --> RAMP_CHALLENGE
        end

        subgraph "Spike Pattern"
            SPIKE_DESC[Sudden burst<br/>10→100→10 users]
            SPIKE_CHAR[Characteristics:<br/>• Unpredictable timing<br/>• High intensity<br/>• Short duration]
            SPIKE_CHALLENGE[Scheduling Challenge:<br/>• Reactive scaling<br/>• Resource elasticity<br/>• QoS maintenance]

            SPIKE_DESC --> SPIKE_CHAR
            SPIKE_CHAR --> SPIKE_CHALLENGE
        end

        subgraph "Periodic Pattern"
            PERIODIC_DESC[Sinusoidal variation<br/>10-100 users, 60s cycles]
            PERIODIC_CHAR[Characteristics:<br/>• Regular oscillation<br/>• Predictable pattern<br/>• Cyclic resource needs]
            PERIODIC_CHALLENGE[Scheduling Challenge:<br/>• Pattern recognition<br/>• Anticipatory scaling<br/>• Cycle optimization]

            PERIODIC_DESC --> PERIODIC_CHAR
            PERIODIC_CHAR --> PERIODIC_CHALLENGE
        end

        subgraph "Random Pattern"
            RANDOM_DESC[Stochastic variation<br/>10-100 users, random timing]
            RANDOM_CHAR[Characteristics:<br/>• Unpredictable changes<br/>• Variable intensity<br/>• No clear pattern]
            RANDOM_CHALLENGE[Scheduling Challenge:<br/>• Adaptive strategies<br/>• Robust resource allocation<br/>• Uncertainty handling]

            RANDOM_DESC --> RANDOM_CHAR
            RANDOM_CHAR --> RANDOM_CHALLENGE
        end
    end

    subgraph "Baseline Performance Results"
        subgraph "GPU vs CPU Comparison"
            GPU_PERF[GPU Performance<br/>1 Pod Running<br/>2 Pods Pending]
            CPU_PERF[CPU Performance<br/>3 Pods Running<br/>Full Capacity]

            FAIRNESS_ISSUE[⚠️ Experimental Fairness Issue<br/>1 GPU Pod vs 3 CPU Pods<br/>Unequal Resource Allocation]

            GPU_PERF --> FAIRNESS_ISSUE
            CPU_PERF --> FAIRNESS_ISSUE
        end

        subgraph "Performance Metrics (⚠️ Under Review)"
            RAMP_RESULT[Ramp: GPU 1.16x speedup<br/>5800ms vs 6700ms P95]
            SPIKE_RESULT[Spike: GPU 1.27x speedup<br/>370ms vs 470ms P95]
            PERIODIC_RESULT[Periodic: Similar performance<br/>2300ms both platforms]
            RANDOM_RESULT[Random: GPU 1.96x speedup<br/>2600ms vs 5100ms P95]

            FAIRNESS_ISSUE --> RAMP_RESULT
            FAIRNESS_ISSUE --> SPIKE_RESULT
            FAIRNESS_ISSUE --> PERIODIC_RESULT
            FAIRNESS_ISSUE --> RANDOM_RESULT
        end
    end

## 6. RL Training Strategy and Expected Improvements

```mermaid
graph LR
    subgraph "RL Learning Objectives"
        subgraph "Pattern-Specific Strategies"
            RAMP_STRATEGY[Ramp Pattern Strategy<br/>• Learn gradual scaling<br/>• Optimize resource pre-allocation<br/>• Target: >1.16x improvement]

            SPIKE_STRATEGY[Spike Pattern Strategy<br/>• Learn burst handling<br/>• Optimize reactive scaling<br/>• Target: >1.27x improvement]

            PERIODIC_STRATEGY[Periodic Pattern Strategy<br/>• Learn pattern recognition<br/>• Optimize cyclic scaling<br/>• Target: Balanced allocation]

            RANDOM_STRATEGY[Random Pattern Strategy<br/>• Learn adaptive strategies<br/>• Optimize uncertainty handling<br/>• Target: >1.96x improvement]
        end

        subgraph "Multi-Objective Optimization"
            LATENCY_OPT[Latency Optimization<br/>Weight: 0.4<br/>Minimize P95 response time]

            THROUGHPUT_OPT[Throughput Optimization<br/>Weight: 0.3<br/>Maximize request rate]

            EFFICIENCY_OPT[Resource Efficiency<br/>Weight: 0.2<br/>Optimal GPU/CPU allocation]

            STABILITY_OPT[System Stability<br/>Weight: 0.1<br/>Minimize scaling thrashing]
        end
    end

    subgraph "Training Process"
        subgraph "Episode Structure"
            EPISODE_INIT[Episode Initialization<br/>• Reset environment<br/>• Select load pattern<br/>• Initialize metrics]

            EPISODE_LOOP[Episode Loop (300s)<br/>• Observe state every 30s<br/>• Select actions<br/>• Execute on Kubernetes<br/>• Calculate rewards]

            EPISODE_END[Episode Completion<br/>• Final evaluation<br/>• Model update<br/>• Save checkpoint]

            EPISODE_INIT --> EPISODE_LOOP
            EPISODE_LOOP --> EPISODE_END
        end

        subgraph "Learning Progress"
            EXPLORATION[Early Episodes (1-20)<br/>• High exploration<br/>• Random actions<br/>• Baseline establishment]

            LEARNING[Mid Episodes (21-60)<br/>• Balanced exploration/exploitation<br/>• Pattern recognition<br/>• Strategy development]

            OPTIMIZATION[Late Episodes (61-100)<br/>• Low exploration<br/>• Policy refinement<br/>• Performance optimization]

            EXPLORATION --> LEARNING
            LEARNING --> OPTIMIZATION
        end
    end

    subgraph "Expected Outcomes"
        subgraph "Performance Improvements"
            LATENCY_IMP[Latency Improvements<br/>• Ramp: >16% reduction<br/>• Spike: >27% reduction<br/>• Random: >96% reduction]

            THROUGHPUT_IMP[Throughput Improvements<br/>• Better resource utilization<br/>• Reduced bottlenecks<br/>• Optimized scaling decisions]

            EFFICIENCY_IMP[Efficiency Gains<br/>• Smart GPU/CPU selection<br/>• Load-aware placement<br/>• Resource waste reduction]
        end

        subgraph "Academic Contributions"
            NOVELTY[Research Novelty<br/>• RL-based Kubernetes scheduling<br/>• Multi-objective optimization<br/>• Real-world evaluation]

            REPRODUCIBILITY[Reproducibility<br/>• Open-source implementation<br/>• Detailed methodology<br/>• Comprehensive evaluation]

            IMPACT[Practical Impact<br/>• Edge computing optimization<br/>• Cost-performance tradeoffs<br/>• Industry applicability]
        end
    end

    RAMP_STRATEGY --> LATENCY_OPT
    SPIKE_STRATEGY --> THROUGHPUT_OPT
    PERIODIC_STRATEGY --> EFFICIENCY_OPT
    RANDOM_STRATEGY --> STABILITY_OPT

    EPISODE_END --> EXPLORATION
    OPTIMIZATION --> LATENCY_IMP
    LATENCY_IMP --> NOVELTY
```
