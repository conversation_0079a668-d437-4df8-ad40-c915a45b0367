#!/usr/bin/env python3
"""
Kubernetes RL Environment for GPU Resource Management
Based on baseline experimental findings showing load pattern sensitivity and GPU advantages.
"""

import gym
import numpy as np
import time
import logging
import json
from typing import Dict, List, Tuple, Optional
from kubernetes import client, config
from prometheus_api_client import PrometheusConnect
import requests
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkloadType(Enum):
    GPU = "gpu"
    CPU = "cpu"
    MIXED = "mixed"

class LoadPattern(Enum):
    RAMP = "ramp"
    SPIKE = "spike"
    PERIODIC = "periodic"
    RANDOM = "random"

@dataclass
class EnvironmentConfig:
    """Configuration for the Kubernetes RL environment"""
    namespace: str = "workloads"
    prometheus_url: str = "http://localhost:9090"
    locust_url: str = "http://localhost:8089"
    max_replicas: int = 10
    min_replicas: int = 1
    observation_window: int = 60  # seconds
    action_interval: int = 30     # seconds
    episode_duration: int = 300   # seconds

    # Reward function weights (based on baseline findings)
    latency_weight: float = 0.4
    throughput_weight: float = 0.3
    resource_efficiency_weight: float = 0.2
    stability_weight: float = 0.1

class KubernetesRLEnvironment(gym.Env):
    """
    Kubernetes RL Environment for intelligent resource management

    Based on baseline findings:
    - GPU shows 1.96x speedup under random loads
    - GPU shows 1.27x speedup under spike loads
    - Load pattern sensitivity requires intelligent scheduling
    - Resource efficiency varies significantly (10-15x CPU difference)
    """

    def __init__(self, config: EnvironmentConfig):
        super().__init__()
        self.config = config

        # Initialize Kubernetes client
        try:
            from kubernetes import config as k8s_config
            k8s_config.load_incluster_config()
            logger.info("Loaded in-cluster Kubernetes config")
        except Exception as e:
            try:
                from kubernetes import config as k8s_config
                k8s_config.load_kube_config()
                logger.info("Loaded local Kubernetes config")
            except Exception as e2:
                logger.error(f"Failed to load Kubernetes config: {e2}")
                # For testing, we can create a mock environment
                logger.warning("Using mock Kubernetes client for testing")
                self._use_mock_k8s = True

        # Initialize Kubernetes API clients
        if not hasattr(self, '_use_mock_k8s'):
            self._use_mock_k8s = False

        if not self._use_mock_k8s:
            self.k8s_apps = client.AppsV1Api()
            self.k8s_core = client.CoreV1Api()
        else:
            # Mock clients for testing
            self.k8s_apps = None
            self.k8s_core = None

        # Initialize Prometheus client
        try:
            self.prometheus = PrometheusConnect(url=config.prometheus_url, disable_ssl=True)
            logger.info("Connected to Prometheus")
        except Exception as e:
            logger.warning(f"Failed to connect to Prometheus: {e}")
            self.prometheus = None

        # Environment state
        self.current_step = 0
        self.episode_start_time = None
        self.current_workload_type = WorkloadType.MIXED
        self.current_load_pattern = LoadPattern.RAMP

        # Define action and observation spaces
        self._define_spaces()

        # Performance tracking
        self.performance_history = []
        self.baseline_metrics = self._load_baseline_metrics()

    def _define_spaces(self):
        """Define action and observation spaces based on baseline analysis"""

        # Observation space: [resource_metrics, performance_metrics, load_characteristics]
        # Based on baseline findings: CPU usage, memory usage, GPU utilization, latency, throughput
        obs_low = np.array([
            0.0,    # cpu_utilization (0-100%)
            0.0,    # memory_utilization (0-100%)
            0.0,    # gpu_utilization (0-100%)
            0.0,    # current_latency_p95 (0-10000ms)
            0.0,    # current_throughput (0-100 req/s)
            0.0,    # current_replicas_gpu (0-10)
            0.0,    # current_replicas_cpu (0-10)
            0.0,    # load_trend (-1 to 1, decreasing to increasing)
            0.0,    # load_variance (0-1, stable to volatile)
            0.0,    # time_in_episode (0-1, normalized)
        ])

        obs_high = np.array([
            100.0,  # cpu_utilization
            100.0,  # memory_utilization
            100.0,  # gpu_utilization
            10000.0, # current_latency_p95
            100.0,  # current_throughput
            10.0,   # current_replicas_gpu
            10.0,   # current_replicas_cpu
            1.0,    # load_trend
            1.0,    # load_variance
            1.0,    # time_in_episode
        ])

        self.observation_space = gym.spaces.Box(low=obs_low, high=obs_high, dtype=np.float32)

        # Action space: [gpu_replica_action, cpu_replica_action, workload_placement]
        # Based on baseline: GPU excels under certain patterns, CPU under others
        self.action_space = gym.spaces.MultiDiscrete([
            5,  # GPU replica action: [scale_down_2, scale_down_1, maintain, scale_up_1, scale_up_2]
            5,  # CPU replica action: [scale_down_2, scale_down_1, maintain, scale_up_1, scale_up_2]
            3,  # Workload placement preference: [prefer_gpu, balanced, prefer_cpu]
        ])

    def _load_baseline_metrics(self) -> Dict:
        """Load baseline performance metrics for comparison"""
        baseline_file = "/home/<USER>/allProjects/ecrl/experiments/results/comparison/comprehensive_comparison_report.txt"

        # Default baseline metrics based on our experimental results
        baseline = {
            "gpu_p95_latency": {
                "random": 2600,
                "spike": 370,
                "ramp": 5800,
                "periodic": 2300
            },
            "cpu_p95_latency": {
                "random": 5100,
                "spike": 470,
                "ramp": 6700,
                "periodic": 2300
            },
            "gpu_throughput": {
                "random": 10.40,
                "spike": 6.40,
                "ramp": 10.30,
                "periodic": 11.50
            },
            "cpu_throughput": {
                "random": 10.80,
                "spike": 5.70,
                "ramp": 10.20,
                "periodic": 12.10
            }
        }

        try:
            # Try to load actual baseline data if available
            with open(baseline_file, 'r') as f:
                content = f.read()
                # Parse baseline data from report
                logger.info("Loaded baseline metrics from experimental results")
        except FileNotFoundError:
            logger.warning("Baseline file not found, using default metrics")

        return baseline

    def reset(self) -> np.ndarray:
        """Reset environment for new episode"""
        logger.info("Resetting RL environment for new episode")

        self.current_step = 0
        self.episode_start_time = time.time()
        self.performance_history = []

        # Randomly select load pattern for this episode
        self.current_load_pattern = np.random.choice(list(LoadPattern))
        logger.info(f"Episode load pattern: {self.current_load_pattern.value}")

        # Reset to baseline configuration
        self._reset_deployments()

        # Wait for system to stabilize
        time.sleep(10)

        # Return initial observation
        return self._get_observation()

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """Execute action and return next state, reward, done, info"""

        # Parse action
        gpu_action, cpu_action, placement_action = action

        # Execute action
        action_info = self._execute_action(gpu_action, cpu_action, placement_action)

        # Wait for action to take effect
        time.sleep(self.config.action_interval)

        # Get new observation
        observation = self._get_observation()

        # Calculate reward
        reward = self._calculate_reward()

        # Check if episode is done
        done = self._is_episode_done()

        # Collect info
        info = {
            "step": self.current_step,
            "load_pattern": self.current_load_pattern.value,
            "action_info": action_info,
            "performance_metrics": self._get_current_performance(),
            "episode_time": time.time() - self.episode_start_time
        }

        self.current_step += 1

        return observation, reward, done, info

    def _execute_action(self, gpu_action: int, cpu_action: int, placement_action: int) -> Dict:
        """Execute the chosen actions on Kubernetes deployments"""

        action_info = {
            "gpu_replica_change": 0,
            "cpu_replica_change": 0,
            "placement_preference": placement_action
        }

        if self._use_mock_k8s:
            # Mock action execution for testing
            gpu_change = self._action_to_replica_change(gpu_action)
            cpu_change = self._action_to_replica_change(cpu_action)
            action_info["gpu_replica_change"] = gpu_change
            action_info["cpu_replica_change"] = cpu_change
            logger.info(f"Mock action executed: GPU change={gpu_change}, CPU change={cpu_change}")
            return action_info

        try:
            # Get current replica counts
            gpu_deployment = self.k8s_apps.read_namespaced_deployment(
                name="mobilenetv4-triton-deployment",
                namespace=self.config.namespace
            )
            cpu_deployment = self.k8s_apps.read_namespaced_deployment(
                name="mobilenetv4-triton-cpu-deployment",
                namespace=self.config.namespace
            )

            current_gpu_replicas = gpu_deployment.spec.replicas
            current_cpu_replicas = cpu_deployment.spec.replicas

            # Calculate new replica counts based on actions
            gpu_change = self._action_to_replica_change(gpu_action)
            cpu_change = self._action_to_replica_change(cpu_action)

            new_gpu_replicas = max(self.config.min_replicas,
                                 min(self.config.max_replicas,
                                     current_gpu_replicas + gpu_change))
            new_cpu_replicas = max(self.config.min_replicas,
                                 min(self.config.max_replicas,
                                     current_cpu_replicas + cpu_change))

            # Apply replica changes
            if new_gpu_replicas != current_gpu_replicas:
                gpu_deployment.spec.replicas = new_gpu_replicas
                self.k8s_apps.patch_namespaced_deployment(
                    name="mobilenetv4-triton-deployment",
                    namespace=self.config.namespace,
                    body=gpu_deployment
                )
                action_info["gpu_replica_change"] = new_gpu_replicas - current_gpu_replicas
                logger.info(f"Scaled GPU deployment: {current_gpu_replicas} -> {new_gpu_replicas}")

            if new_cpu_replicas != current_cpu_replicas:
                cpu_deployment.spec.replicas = new_cpu_replicas
                self.k8s_apps.patch_namespaced_deployment(
                    name="mobilenetv4-triton-cpu-deployment",
                    namespace=self.config.namespace,
                    body=cpu_deployment
                )
                action_info["cpu_replica_change"] = new_cpu_replicas - current_cpu_replicas
                logger.info(f"Scaled CPU deployment: {current_cpu_replicas} -> {new_cpu_replicas}")

        except Exception as e:
            logger.error(f"Error executing action: {e}")

        return action_info

    def _action_to_replica_change(self, action: int) -> int:
        """Convert action index to replica change"""
        action_map = {
            0: -2,  # scale_down_2
            1: -1,  # scale_down_1
            2: 0,   # maintain
            3: 1,   # scale_up_1
            4: 2,   # scale_up_2
        }
        return action_map.get(action, 0)

    def _get_observation(self) -> np.ndarray:
        """Get current system observation"""

        try:
            # Get resource utilization metrics
            cpu_util = self._get_cpu_utilization()
            memory_util = self._get_memory_utilization()
            gpu_util = self._get_gpu_utilization()

            # Get performance metrics
            latency_p95 = self._get_current_latency_p95()
            throughput = self._get_current_throughput()

            # Get current replica counts
            gpu_replicas, cpu_replicas = self._get_current_replicas()

            # Get load characteristics
            load_trend = self._get_load_trend()
            load_variance = self._get_load_variance()

            # Get episode progress
            episode_progress = min(1.0, (time.time() - self.episode_start_time) / self.config.episode_duration)

            observation = np.array([
                cpu_util,
                memory_util,
                gpu_util,
                latency_p95,
                throughput,
                gpu_replicas,
                cpu_replicas,
                load_trend,
                load_variance,
                episode_progress
            ], dtype=np.float32)

        except Exception as e:
            logger.error(f"Error getting observation: {e}")
            # Return safe default observation
            observation = np.zeros(10, dtype=np.float32)

        return observation

    def _calculate_reward(self) -> float:
        """
        Calculate reward based on baseline findings and multi-objective optimization

        Reward components:
        1. Latency improvement vs baseline
        2. Throughput improvement vs baseline
        3. Resource efficiency (based on baseline CPU usage patterns)
        4. System stability (avoid thrashing)
        """

        try:
            # Get current performance metrics
            current_latency = self._get_current_latency_p95()
            current_throughput = self._get_current_throughput()
            current_cpu_util = self._get_cpu_utilization()

            # Get baseline performance for current load pattern
            pattern_name = self.current_load_pattern.value
            baseline_gpu_latency = self.baseline_metrics["gpu_p95_latency"].get(pattern_name, 3000)
            baseline_cpu_latency = self.baseline_metrics["cpu_p95_latency"].get(pattern_name, 4000)
            baseline_gpu_throughput = self.baseline_metrics["gpu_throughput"].get(pattern_name, 10)
            baseline_cpu_throughput = self.baseline_metrics["cpu_throughput"].get(pattern_name, 10)

            # Choose better baseline based on pattern (from our experimental findings)
            if pattern_name in ["random", "spike"]:
                # GPU performs better under these patterns
                baseline_latency = baseline_gpu_latency
                baseline_throughput = baseline_gpu_throughput
            else:
                # Use average of both for other patterns
                baseline_latency = (baseline_gpu_latency + baseline_cpu_latency) / 2
                baseline_throughput = (baseline_gpu_throughput + baseline_cpu_throughput) / 2

            # Calculate reward components

            # 1. Latency reward (lower is better)
            latency_improvement = max(0, (baseline_latency - current_latency) / baseline_latency)
            latency_reward = latency_improvement * self.config.latency_weight

            # 2. Throughput reward (higher is better)
            throughput_improvement = max(0, (current_throughput - baseline_throughput) / baseline_throughput)
            throughput_reward = throughput_improvement * self.config.throughput_weight

            # 3. Resource efficiency reward (based on baseline findings of 10-15x CPU difference)
            # Reward efficient resource usage
            gpu_replicas, cpu_replicas = self._get_current_replicas()
            total_replicas = gpu_replicas + cpu_replicas

            if total_replicas > 0:
                # Prefer GPU under appropriate conditions (random, spike patterns)
                if pattern_name in ["random", "spike"]:
                    gpu_ratio = gpu_replicas / total_replicas
                    efficiency_reward = gpu_ratio * self.config.resource_efficiency_weight
                else:
                    # Balanced approach for other patterns
                    efficiency_reward = (1 - abs(gpu_replicas - cpu_replicas) / total_replicas) * self.config.resource_efficiency_weight
            else:
                efficiency_reward = 0

            # 4. Stability reward (penalize frequent changes)
            stability_reward = self._calculate_stability_reward()

            # Combine rewards
            total_reward = latency_reward + throughput_reward + efficiency_reward + stability_reward

            # Add penalty for extreme resource usage
            if current_cpu_util > 90:
                total_reward -= 0.5  # Penalty for overutilization

            logger.debug(f"Reward components - Latency: {latency_reward:.3f}, "
                        f"Throughput: {throughput_reward:.3f}, "
                        f"Efficiency: {efficiency_reward:.3f}, "
                        f"Stability: {stability_reward:.3f}, "
                        f"Total: {total_reward:.3f}")

        except Exception as e:
            logger.error(f"Error calculating reward: {e}")
            total_reward = -1.0  # Penalty for errors

        return total_reward

    def _calculate_stability_reward(self) -> float:
        """Calculate stability reward to prevent thrashing"""
        if len(self.performance_history) < 2:
            return 0

        # Check for frequent scaling actions
        recent_actions = self.performance_history[-5:]  # Last 5 actions
        if len(recent_actions) >= 3:
            # Penalize if too many scaling actions in recent history
            scaling_actions = sum(1 for action in recent_actions if action.get("replica_changes", 0) > 0)
            if scaling_actions > 2:
                return -0.2 * self.config.stability_weight

        return 0.1 * self.config.stability_weight  # Small reward for stability

    def _get_cpu_utilization(self) -> float:
        """Get current CPU utilization from Prometheus"""
        if self._use_mock_k8s or not self.prometheus:
            # Return mock values for testing
            return 45.0 + np.random.normal(0, 5)  # Simulate varying CPU usage

        try:
            query = 'avg(100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100))'
            result = self.prometheus.custom_query(query)
            if result:
                return float(result[0]['value'][1])
        except Exception as e:
            logger.error(f"Error getting CPU utilization: {e}")
        return 50.0  # Default value

    def _get_memory_utilization(self) -> float:
        """Get current memory utilization from Prometheus"""
        if self._use_mock_k8s or not self.prometheus:
            return 60.0 + np.random.normal(0, 8)  # Simulate varying memory usage

        try:
            query = '(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100'
            result = self.prometheus.custom_query(query)
            if result:
                return float(result[0]['value'][1])
        except Exception as e:
            logger.error(f"Error getting memory utilization: {e}")
        return 50.0  # Default value

    def _get_gpu_utilization(self) -> float:
        """Get current GPU utilization from DCGM metrics"""
        if self._use_mock_k8s or not self.prometheus:
            return 35.0 + np.random.normal(0, 10)  # Simulate varying GPU usage

        try:
            query = 'DCGM_FI_DEV_GPU_UTIL'
            result = self.prometheus.custom_query(query)
            if result:
                return float(result[0]['value'][1])
        except Exception as e:
            logger.error(f"Error getting GPU utilization: {e}")
        return 30.0  # Default value based on baseline (43% during inference)

    def _get_current_latency_p95(self) -> float:
        """Get current P95 latency from Locust metrics"""
        if self._use_mock_k8s:
            # Simulate latency based on current load pattern
            base_latency = {
                'random': 2600,
                'spike': 370,
                'ramp': 5800,
                'periodic': 2300
            }.get(self.current_load_pattern.value if hasattr(self, 'current_load_pattern') else 'ramp', 2000)
            return base_latency + np.random.normal(0, base_latency * 0.1)

        try:
            # Try to get from Locust API
            response = requests.get(f"{self.config.locust_url}/stats/requests", timeout=5)
            if response.status_code == 200:
                stats = response.json()
                if stats.get('stats'):
                    for stat in stats['stats']:
                        if stat.get('name') == 'Aggregated':
                            return stat.get('current_response_time_percentile_95', 1000)
        except Exception as e:
            logger.error(f"Error getting latency: {e}")
        return 1000.0  # Default value

    def _get_current_throughput(self) -> float:
        """Get current throughput from Locust metrics"""
        if self._use_mock_k8s:
            # Simulate throughput based on current load pattern
            base_throughput = {
                'random': 10.4,
                'spike': 6.4,
                'ramp': 10.3,
                'periodic': 11.5
            }.get(self.current_load_pattern.value if hasattr(self, 'current_load_pattern') else 'ramp', 8.0)
            return base_throughput + np.random.normal(0, base_throughput * 0.1)

        try:
            response = requests.get(f"{self.config.locust_url}/stats/requests", timeout=5)
            if response.status_code == 200:
                stats = response.json()
                if stats.get('stats'):
                    for stat in stats['stats']:
                        if stat.get('name') == 'Aggregated':
                            return stat.get('current_rps', 5.0)
        except Exception as e:
            logger.error(f"Error getting throughput: {e}")
        return 5.0  # Default value

    def _get_current_replicas(self) -> Tuple[int, int]:
        """Get current replica counts for GPU and CPU deployments"""
        if self._use_mock_k8s:
            # Return mock values for testing
            return 3, 3

        try:
            gpu_deployment = self.k8s_apps.read_namespaced_deployment(
                name="mobilenetv4-triton-deployment",
                namespace=self.config.namespace
            )
            cpu_deployment = self.k8s_apps.read_namespaced_deployment(
                name="mobilenetv4-triton-cpu-deployment",
                namespace=self.config.namespace
            )
            return gpu_deployment.spec.replicas, cpu_deployment.spec.replicas
        except Exception as e:
            logger.error(f"Error getting replica counts: {e}")
            return 3, 3  # Default values

    def _get_load_trend(self) -> float:
        """Calculate load trend from recent throughput history"""
        if len(self.performance_history) < 3:
            return 0.0

        recent_throughputs = [h.get("throughput", 5) for h in self.performance_history[-3:]]
        if len(recent_throughputs) >= 2:
            trend = (recent_throughputs[-1] - recent_throughputs[0]) / max(recent_throughputs[0], 1)
            return np.clip(trend, -1.0, 1.0)
        return 0.0

    def _get_load_variance(self) -> float:
        """Calculate load variance from recent history"""
        if len(self.performance_history) < 3:
            return 0.0

        recent_throughputs = [h.get("throughput", 5) for h in self.performance_history[-5:]]
        if len(recent_throughputs) >= 3:
            variance = np.var(recent_throughputs) / max(np.mean(recent_throughputs), 1)
            return np.clip(variance, 0.0, 1.0)
        return 0.0

    def _get_current_performance(self) -> Dict:
        """Get current performance metrics for tracking"""
        performance = {
            "latency_p95": self._get_current_latency_p95(),
            "throughput": self._get_current_throughput(),
            "cpu_utilization": self._get_cpu_utilization(),
            "memory_utilization": self._get_memory_utilization(),
            "gpu_utilization": self._get_gpu_utilization(),
            "gpu_replicas": self._get_current_replicas()[0],
            "cpu_replicas": self._get_current_replicas()[1],
            "timestamp": time.time()
        }

        # Add to history
        self.performance_history.append(performance)

        # Keep only recent history (last 20 entries)
        if len(self.performance_history) > 20:
            self.performance_history = self.performance_history[-20:]

        return performance

    def _reset_deployments(self):
        """Reset deployments to baseline configuration"""
        if self._use_mock_k8s:
            logger.info("Mock reset deployments to baseline configuration (3 replicas each)")
            return

        try:
            # Reset to 3 replicas each (baseline configuration)
            gpu_deployment = self.k8s_apps.read_namespaced_deployment(
                name="mobilenetv4-triton-deployment",
                namespace=self.config.namespace
            )
            cpu_deployment = self.k8s_apps.read_namespaced_deployment(
                name="mobilenetv4-triton-cpu-deployment",
                namespace=self.config.namespace
            )

            gpu_deployment.spec.replicas = 3
            cpu_deployment.spec.replicas = 3

            self.k8s_apps.patch_namespaced_deployment(
                name="mobilenetv4-triton-deployment",
                namespace=self.config.namespace,
                body=gpu_deployment
            )
            self.k8s_apps.patch_namespaced_deployment(
                name="mobilenetv4-triton-cpu-deployment",
                namespace=self.config.namespace,
                body=cpu_deployment
            )

            logger.info("Reset deployments to baseline configuration (3 replicas each)")

        except Exception as e:
            logger.error(f"Error resetting deployments: {e}")

    def _is_episode_done(self) -> bool:
        """Check if episode is complete"""
        elapsed_time = time.time() - self.episode_start_time
        return elapsed_time >= self.config.episode_duration

    def close(self):
        """Clean up environment"""
        logger.info("Closing Kubernetes RL environment")
        # Reset to baseline state
        self._reset_deployments()

    def get_episode_summary(self) -> Dict:
        """Get summary of episode performance"""
        if not self.performance_history:
            return {}

        latencies = [h["latency_p95"] for h in self.performance_history]
        throughputs = [h["throughput"] for h in self.performance_history]

        return {
            "episode_duration": time.time() - self.episode_start_time,
            "load_pattern": self.current_load_pattern.value,
            "total_steps": self.current_step,
            "avg_latency": np.mean(latencies),
            "min_latency": np.min(latencies),
            "max_latency": np.max(latencies),
            "avg_throughput": np.mean(throughputs),
            "min_throughput": np.min(throughputs),
            "max_throughput": np.max(throughputs),
            "final_gpu_replicas": self.performance_history[-1]["gpu_replicas"],
            "final_cpu_replicas": self.performance_history[-1]["cpu_replicas"]
        }