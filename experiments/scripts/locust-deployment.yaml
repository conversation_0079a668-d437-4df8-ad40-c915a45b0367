apiVersion: apps/v1
kind: Deployment
metadata:
  name: locust-master
  namespace: workloads
spec:
  replicas: 1
  selector:
    matchLabels:
      app: locust-master
  template:
    metadata:
      labels:
        app: locust-master
    spec:
      containers:
      - name: locust-master
        image: locustio/locust:latest
        command: ["/bin/sh", "-c"]
        args:
          - |
            pip install numpy tritonclient[http] && \
            locust
        ports:
        - containerPort: 8089
        - containerPort: 5557
        env:
        - name: LOCUST_MODE
          value: "master"
        - name: LOCUST_MASTER_BIND_HOST
          value: "0.0.0.0"
        - name: LOCUST_MASTER_BIND_PORT
          value: "5557"
        volumeMounts:
        - name: locustfile-volume
          mountPath: /home/<USER>
      volumes:
      - name: locustfile-volume
        configMap:
          name: locustfile-config
          items:
          - key: locustfile.py
            path: locustfile.py
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: locust-worker
  namespace: workloads
spec:
  replicas: 2  # Adjust based on your needs
  selector:
    matchLabels:
      app: locust-worker
  template:
    metadata:
      labels:
        app: locust-worker
    spec:
      containers:
      - name: locust-worker
        image: locustio/locust:latest
        command: ["/bin/sh", "-c"]
        args:
          - |
            pip install numpy tritonclient[http] && \
            locust
        env:
        - name: LOCUST_MODE
          value: "worker"
        - name: LOCUST_MASTER_HOST
          value: "locust-master"
        - name: LOCUST_MASTER_PORT
          value: "5557"
        volumeMounts:
        - name: locustfile-volume
          mountPath: /home/<USER>
      volumes:
      - name: locustfile-volume
        configMap:
          name: locustfile-config
          items:
          - key: locustfile.py
            path: locustfile.py
---
apiVersion: v1
kind: Service
metadata:
  name: locust-master
  namespace: workloads
spec:
  selector:
    app: locust-master
  ports:
  - port: 8089
    targetPort: 8089
    name: web
  - port: 5557
    targetPort: 5557
    name: master
  type: ClusterIP