#!/usr/bin/env python3
"""
Generate a comprehensive performance comparison report for different load patterns
between GPU and CPU versions of the Triton server.
"""

import json
import os
import glob
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import pandas as pd
import seaborn as sns
from matplotlib.ticker import MaxNLocator

# Directory paths
RESULTS_DIR = 'results'
GPU_DIR = os.path.join(RESULTS_DIR, 'baseline')
CPU_DIR = os.path.join(RESULTS_DIR, 'cpu_baseline')
REPORT_DIR = os.path.join(RESULTS_DIR, 'comprehensive_report')

# Create report directory if it doesn't exist
os.makedirs(REPORT_DIR, exist_ok=True)

# Load synthetic test results
def load_synthetic_results():
    with open(os.path.join(GPU_DIR, 'metrics/synthetic_results.json'), 'r') as f:
        gpu_results = json.load(f)
    with open(os.path.join(CPU_DIR, 'metrics/synthetic_results.json'), 'r') as f:
        cpu_results = json.load(f)
    return gpu_results, cpu_results

# Generate synthetic test comparison report
def generate_synthetic_report(gpu_results, cpu_results):
    # Extract metrics
    gpu_latency_avg = gpu_results['avg_latency_ms']
    gpu_latency_p95 = gpu_results['p95_latency_ms']
    gpu_throughput = gpu_results['samples_per_second']

    cpu_latency_avg = cpu_results['avg_latency_ms']
    cpu_latency_p95 = cpu_results['p95_latency_ms']
    cpu_throughput = cpu_results['samples_per_second']

    # Calculate speedups
    latency_speedup = cpu_latency_avg / gpu_latency_avg if gpu_latency_avg > 0 else float('inf')
    throughput_speedup = gpu_throughput / cpu_throughput if cpu_throughput > 0 else float('inf')

    # Generate text report
    report = f'Synthetic Test Performance Comparison\n'
    report += f'=====================================\n'
    report += f'CPU Average Latency: {cpu_latency_avg:.2f} ms\n'
    report += f'GPU Average Latency: {gpu_latency_avg:.2f} ms\n'
    report += f'Latency Speedup (CPU/GPU): {latency_speedup:.2f}x\n\n'
    report += f'CPU P95 Latency: {cpu_latency_p95:.2f} ms\n'
    report += f'GPU P95 Latency: {gpu_latency_p95:.2f} ms\n\n'
    report += f'CPU Throughput: {cpu_throughput:.2f} images/sec\n'
    report += f'GPU Throughput: {gpu_throughput:.2f} images/sec\n'
    report += f'Throughput Speedup (GPU/CPU): {throughput_speedup:.2f}x\n'

    # Save report
    with open(os.path.join(REPORT_DIR, 'synthetic_report.txt'), 'w') as f:
        f.write(report)

    # Create bar charts
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # Latency comparison
    platforms = ['CPU', 'GPU']
    avg_latencies = [cpu_latency_avg, gpu_latency_avg]
    p95_latencies = [cpu_latency_p95, gpu_latency_p95]

    x = np.arange(len(platforms))
    width = 0.35

    ax1.bar(x - width/2, avg_latencies, width, label='Avg Latency')
    ax1.bar(x + width/2, p95_latencies, width, label='P95 Latency')
    ax1.set_ylabel('Latency (ms)')
    ax1.set_title('Synthetic Test: Latency Comparison')
    ax1.set_xticks(x)
    ax1.set_xticklabels(platforms)
    ax1.legend()

    # Throughput comparison
    throughputs = [cpu_throughput, gpu_throughput]
    ax2.bar(platforms, throughputs)
    ax2.set_ylabel('Throughput (images/sec)')
    ax2.set_title('Synthetic Test: Throughput Comparison')

    plt.tight_layout()
    plt.savefig(os.path.join(REPORT_DIR, 'synthetic_comparison.png'))
    plt.close()

    return report

# Find the latest dynamic load test files for each pattern
def find_latest_dynamic_files(pattern, base_dir):
    stats_files = glob.glob(os.path.join(base_dir, 'dynamic', f'{pattern}_*_stats.json'))
    users_files = glob.glob(os.path.join(base_dir, 'dynamic', f'{pattern}_*_users.json'))

    if not stats_files or not users_files:
        print(f"No {pattern} files found in {base_dir}/dynamic/")
        print(f"Looking for: {os.path.join(base_dir, 'dynamic', f'{pattern}_*_stats.json')}")
        print(f"And: {os.path.join(base_dir, 'dynamic', f'{pattern}_*_users.json')}")
        return None, None

    latest_stats = max(stats_files, key=os.path.getctime)
    latest_users = max(users_files, key=os.path.getctime)

    print(f"Found {pattern} files: {latest_stats} and {latest_users}")
    return latest_stats, latest_users

# Load dynamic load test results
def load_dynamic_results(pattern):
    gpu_stats_file, gpu_users_file = find_latest_dynamic_files(pattern, GPU_DIR)
    cpu_stats_file, cpu_users_file = find_latest_dynamic_files(pattern, CPU_DIR)

    if not all([gpu_stats_file, gpu_users_file, cpu_stats_file, cpu_users_file]):
        print(f"Warning: Missing files for {pattern} pattern")
        return None, None, None, None

    with open(gpu_stats_file, 'r') as f:
        gpu_stats = json.load(f)
    with open(gpu_users_file, 'r') as f:
        gpu_users = json.load(f)
    with open(cpu_stats_file, 'r') as f:
        cpu_stats = json.load(f)
    with open(cpu_users_file, 'r') as f:
        cpu_users = json.load(f)

    return gpu_stats, gpu_users, cpu_stats, cpu_users

# Generate dynamic load test comparison report
def generate_dynamic_report(pattern):
    gpu_stats, gpu_users, cpu_stats, cpu_users = load_dynamic_results(pattern)

    if not all([gpu_stats, gpu_users, cpu_stats, cpu_users]):
        return f"Could not generate report for {pattern} pattern due to missing data."

    # Extract timestamps and convert to datetime objects
    gpu_timestamps = [datetime.fromisoformat(ts) for ts in gpu_users['timestamps']]
    cpu_timestamps = [datetime.fromisoformat(ts) for ts in cpu_users['timestamps']]

    # Extract user counts
    gpu_user_counts = gpu_users['user_counts']
    cpu_user_counts = cpu_users['user_counts']

    # Extract response times from stats
    gpu_response_times = []
    cpu_response_times = []

    for stat in gpu_stats:
        if 'current_response_time_percentiles' in stat:
            gpu_response_times.append(stat['current_response_time_percentiles'].get('response_time_percentile_0.95', 0))

    for stat in cpu_stats:
        if 'current_response_time_percentiles' in stat:
            cpu_response_times.append(stat['current_response_time_percentiles'].get('response_time_percentile_0.95', 0))

    # Ensure we have the same number of data points
    min_len = min(len(gpu_user_counts), len(gpu_response_times), len(cpu_user_counts), len(cpu_response_times))

    gpu_user_counts = gpu_user_counts[:min_len]
    gpu_response_times = gpu_response_times[:min_len]
    cpu_user_counts = cpu_user_counts[:min_len]
    cpu_response_times = cpu_response_times[:min_len]

    # Calculate average response times
    gpu_avg_response = sum(gpu_response_times) / len(gpu_response_times) if gpu_response_times else 0
    cpu_avg_response = sum(cpu_response_times) / len(cpu_response_times) if cpu_response_times else 0

    # Calculate max response times
    gpu_max_response = max(gpu_response_times) if gpu_response_times else 0
    cpu_max_response = max(cpu_response_times) if cpu_response_times else 0

    # Calculate speedup
    response_speedup = cpu_avg_response / gpu_avg_response if gpu_avg_response > 0 else float('inf')

    # Generate text report
    report = f'{pattern.capitalize()} Pattern Performance Comparison\n'
    report += f'{"=" * (len(pattern) + 32)}\n'
    report += f'CPU Average P95 Response Time: {cpu_avg_response:.2f} ms\n'
    report += f'GPU Average P95 Response Time: {gpu_avg_response:.2f} ms\n'
    report += f'Response Time Speedup (CPU/GPU): {response_speedup:.2f}x\n\n'
    report += f'CPU Maximum P95 Response Time: {cpu_max_response:.2f} ms\n'
    report += f'GPU Maximum P95 Response Time: {gpu_max_response:.2f} ms\n'

    # Save report
    with open(os.path.join(REPORT_DIR, f'{pattern}_report.txt'), 'w') as f:
        f.write(report)

    # Create comparison chart
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)

    # User count plot
    ax1.plot(range(len(gpu_user_counts)), gpu_user_counts, 'b-', label='User Count (GPU Test)')
    ax1.plot(range(len(cpu_user_counts)), cpu_user_counts, 'r--', label='User Count (CPU Test)')
    ax1.set_ylabel('Number of Users')
    ax1.set_title(f'{pattern.capitalize()} Pattern: User Count Over Time')
    ax1.legend()
    ax1.grid(True)

    # Response time plot
    ax2.plot(range(len(gpu_response_times)), gpu_response_times, 'b-', label='GPU P95 Response Time')
    ax2.plot(range(len(cpu_response_times)), cpu_response_times, 'r--', label='CPU P95 Response Time')
    ax2.set_ylabel('P95 Response Time (ms)')
    ax2.set_xlabel('Time Steps')
    ax2.set_title(f'{pattern.capitalize()} Pattern: P95 Response Time Comparison')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(REPORT_DIR, f'{pattern}_comparison.png'))
    plt.close()

    return report

# Generate comprehensive comparison report
def generate_comprehensive_report():
    # Load synthetic test results
    gpu_synthetic, cpu_synthetic = load_synthetic_results()

    # Generate synthetic test report
    synthetic_report = generate_synthetic_report(gpu_synthetic, cpu_synthetic)

    # Generate dynamic load test reports
    patterns = ['ramp', 'spike', 'periodic', 'random']
    dynamic_reports = {}

    for pattern in patterns:
        try:
            report = generate_dynamic_report(pattern)
            dynamic_reports[pattern] = report
        except Exception as e:
            print(f"Error generating report for {pattern} pattern: {e}")
            dynamic_reports[pattern] = f"Error generating report for {pattern} pattern: {e}"

    # Generate comprehensive report
    report = "Comprehensive Performance Comparison Report\n"
    report += "=========================================\n\n"
    report += "This report compares the performance of GPU and CPU versions of the Triton server\n"
    report += "under different load patterns.\n\n"

    report += "1. Synthetic Test Results\n"
    report += "------------------------\n"
    report += synthetic_report + "\n\n"

    for pattern in patterns:
        if pattern in dynamic_reports:
            report += f"2. {pattern.capitalize()} Pattern Results\n"
            report += f"{'-' * (len(pattern) + 19)}\n"
            report += dynamic_reports[pattern] + "\n\n"

    report += "Summary\n"
    report += "-------\n"
    report += "Based on the test results, the GPU version consistently outperforms the CPU version\n"
    report += "in terms of latency and throughput across different load patterns.\n\n"

    # Save comprehensive report
    with open(os.path.join(REPORT_DIR, 'comprehensive_report.txt'), 'w') as f:
        f.write(report)

    print(f"Comprehensive report generated in {REPORT_DIR}")
    return report

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Generate comprehensive performance comparison report')
    parser.add_argument('--gpu-only', action='store_true', help='Generate report for GPU version only')
    parser.add_argument('--cpu-only', action='store_true', help='Generate report for CPU version only')
    args = parser.parse_args()

    if args.gpu_only:
        print("Generating GPU-only report...")
        # TODO: Implement GPU-only report generation
        generate_comprehensive_report()
    elif args.cpu_only:
        print("Generating CPU-only report...")
        # TODO: Implement CPU-only report generation
        generate_comprehensive_report()
    else:
        print("Generating comprehensive report for both GPU and CPU...")
        generate_comprehensive_report()
