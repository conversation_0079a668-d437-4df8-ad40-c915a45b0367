{"timestamp": 1748061521.4410906, "episode_rewards": [0.42000000000000004, 1.7607692307692306, 1.7243478260869565, 1.7607692307692306, 1.4940384615384614, 1.7879071481208548, 2.1604347826086956, 1.8870596904937362, 1.7607692307692306, 1.7607692307692306, 2.71, 2.1604347826086956, 1.7607692307692306, 2.71, 1.7607692307692306, 2.71, 1.7607692307692306, 2.71, 2.71], "episode_lengths": [4, 5, 4, 5, 4, 4, 5, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "episode_summaries": [{"episode_duration": 321.47372007369995, "load_pattern": "spike", "total_steps": 4, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.42000000000000004, "training": true}, {"episode_duration": 388.6589901447296, "load_pattern": "random", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.220000000000001, "min_throughput": 5.0, "max_throughput": 6.1, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7607692307692306, "training": true}, {"episode_duration": 330.00883865356445, "load_pattern": "periodic", "total_steps": 4, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 7.8999999999999995, "min_throughput": 5.0, "max_throughput": 10.3, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7243478260869565, "training": true}, {"episode_duration": 388.3188900947571, "load_pattern": "random", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7607692307692306, "training": true}, {"episode_duration": 324.40281343460083, "load_pattern": "random", "total_steps": 4, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 10.35, "min_throughput": 5.0, "max_throughput": 15.9, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.4940384615384614, "training": true}, {"episode_duration": 321.11041498184204, "load_pattern": "periodic", "total_steps": 4, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 7.525, "min_throughput": 5.0, "max_throughput": 11.3, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7879071481208548, "training": true}, {"episode_duration": 388.3021914958954, "load_pattern": "periodic", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 2.1604347826086956, "training": true}, {"episode_duration": 318.5823905467987, "load_pattern": "periodic", "total_steps": 4, "avg_latency": 1000.0, "min_latency": "1000", "max_latency": "1000", "avg_throughput": 12.825, "min_throughput": 9.8, "max_throughput": 16.2, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.8870596904937362, "training": true}, {"episode_duration": 388.81652998924255, "load_pattern": "random", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7607692307692306, "training": true}, {"episode_duration": 388.3143925666809, "load_pattern": "random", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7607692307692306, "training": true}, {"episode_duration": 388.32260632514954, "load_pattern": "ramp", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 2.71, "training": true}, {"episode_duration": 388.3389883041382, "load_pattern": "periodic", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 2.1604347826086956, "training": true}, {"episode_duration": 388.3383526802063, "load_pattern": "random", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7607692307692306, "training": true}, {"episode_duration": 388.3284327983856, "load_pattern": "ramp", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 2.71, "training": true}, {"episode_duration": 388.33698749542236, "load_pattern": "random", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7607692307692306, "training": true}, {"episode_duration": 388.3256471157074, "load_pattern": "ramp", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 2.71, "training": true}, {"episode_duration": 388.34400248527527, "load_pattern": "random", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 1.7607692307692306, "training": true}, {"episode_duration": 388.33316373825073, "load_pattern": "ramp", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 2.71, "training": true}, {"episode_duration": 388.5526831150055, "load_pattern": "ramp", "total_steps": 5, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 2.71, "training": true}], "training_metrics": [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {"policy_loss": 5.404627501964569, "value_loss": 2136.1695923805237, "entropy_loss": -1.3942699359260902e-06, "total_loss": 1073.4894236782836}, {}, {}, {}, {}, {}], "agent_stats": {"policy_losses_mean": 5.404627501964569, "policy_losses_std": 0.0, "policy_losses_recent": 5.404627501964569, "value_losses_mean": 2136.1695923805237, "value_losses_std": 0.0, "value_losses_recent": 2136.1695923805237, "entropy_losses_mean": -1.3942699359260902e-06, "entropy_losses_std": 0.0, "entropy_losses_recent": -1.3942699359260902e-06}}