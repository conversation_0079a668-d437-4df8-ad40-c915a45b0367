{"timestamp": 1748052105.0170164, "episode_rewards": [0.4260869565217391, 0.4260869565217391, 0.4260869565217391, 0.536, 0.34615384615384615, 0.4260869565217391, 0.536, 0.4260869565217391, 0.34615384615384615, 0.1], "episode_lengths": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "episode_summaries": [{"episode_duration": 85.30202031135559, "load_pattern": "periodic", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.4260869565217391, "training": true}, {"episode_duration": 85.08458471298218, "load_pattern": "periodic", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.4260869565217391, "training": true}, {"episode_duration": 85.08139872550964, "load_pattern": "periodic", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.4260869565217391, "training": true}, {"episode_duration": 85.08591914176941, "load_pattern": "ramp", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.536, "training": true}, {"episode_duration": 85.08660626411438, "load_pattern": "random", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.34615384615384615, "training": true}, {"episode_duration": 85.08485341072083, "load_pattern": "periodic", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.4260869565217391, "training": true}, {"episode_duration": 85.0894067287445, "load_pattern": "ramp", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.536, "training": true}, {"episode_duration": 85.0863299369812, "load_pattern": "periodic", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.4260869565217391, "training": true}, {"episode_duration": 85.08179998397827, "load_pattern": "random", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.34615384615384615, "training": true}, {"episode_duration": 85.0824601650238, "load_pattern": "spike", "total_steps": 1, "avg_latency": 1000.0, "min_latency": 1000.0, "max_latency": 1000.0, "avg_throughput": 5.0, "min_throughput": 5.0, "max_throughput": 5.0, "final_gpu_replicas": 3, "final_cpu_replicas": 3, "episode_reward": 0.1, "training": true}], "training_metrics": [{}, {}, {}, {}, {}, {}, {}, {}, {}], "agent_stats": {}}