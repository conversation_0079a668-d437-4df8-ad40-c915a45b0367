INFO:kubernetes_env:Loaded local Kubernetes config
INFO:kubernetes_env:Connected to Prometheus
/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/gym/spaces/box.py:127: UserWarning: [33mWARN: Box bound precision lowered by casting to float32[0m
  logger.warn(f"Box bound precision lowered by casting to {self.dtype}")
INFO:kubernetes_env:Loaded baseline metrics from experimental results
INFO:ppo_agent:Initialized PPO agent with 36238 parameters
INFO:__main__:Initialized RL trainer with device: cuda
INFO:__main__:Results will be saved to: /home/<USER>/allProjects/ecrl/experiments/rl/training_20250523_214725
INFO:__main__:Starting RL training for 10 episodes
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: periodic
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'b2c02131-dfc7-45e8-8c20-9eb42b4cf6c8', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:47:27 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c239a90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47fe0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47080>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47ec0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47a70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f478c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47980>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c584860>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47fe0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f472c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f45d00>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47dd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c261520>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c7a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'e0e9fd53-6c26-4289-b290-bff01372cea8', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:47:55 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'b44f422e-7159-49fb-b0ae-2451badcb415', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:47:55 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c1ead20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c4c0920>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47740>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46330>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f477d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f472f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f476b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47d70>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9ca10>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d070>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9cfb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9cef0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d910>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47cb0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '13771331-4b9d-41c2-a54c-eb495bf56f0e', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:48:28 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47080>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c41efc0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dd90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c650>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d2b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d9a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '47c20f66-46cd-46c1-b4c9-9524422622c3', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:48:34 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46690>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47e60>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f475f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d2b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dee0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d280>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d490>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dca0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c380>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dfd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c500>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9db20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c590>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c740>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '4fa96f0e-8837-43da-afb2-367ebf2eb5f2', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:48:52 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '007db5b1-7bf3-495a-8951-1e8430c7782f', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:48:52 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 1/10 - Reward: 0.426, Length: 1, Avg Reward (10): 0.426, Time: 85.3s, Pattern: periodic
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: periodic
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '714db34f-7034-4678-87ed-15f2bd342514', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:48:52 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47cb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47e60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46ed0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46fc0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47e00>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f472c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9e2a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9e480>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9de50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dd90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9ddc0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dd30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dc10>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46f30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '08bfefe9-877a-4e5b-86e2-9677f0ddfb04', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:49:20 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '9ed65847-9caa-4660-8a6a-ada0094fc382', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:49:20 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c377320>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47050>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f45cd0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47cb0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46060>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f473e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dc40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d3a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d610>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d760>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9e210>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9e090>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9e360>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f476e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '2c4acd97-ce35-442b-a752-d19fdb6ccb19', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:49:53 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47260>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f470b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9e000>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9db50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9ce30>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c590>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '3feec027-b948-4889-b5c8-6b119f0f97ee', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:49:59 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c100800>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47dd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47200>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f47800>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46960>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f46090>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9cd40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d610>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c1a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d490>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9c530>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dfa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9cef0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dd60>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'f9167d01-a7b8-42d8-8cbe-084fedc5c09b', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:50:17 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '06c0fe3a-87ff-4aca-b288-567da696e691', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:50:17 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 2/10 - Reward: 0.426, Length: 1, Avg Reward (10): 0.426, Time: 85.1s, Pattern: periodic
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: periodic
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'd23d82a0-3b9e-482d-86c8-ee2279addb51', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:50:18 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e74f80>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec3a40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e16e70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d44b60>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0d430>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e8c530>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e8fb90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d249e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea2720>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea1850>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea01a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea1790>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d7ec00>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d7e420>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'c824cda8-d7c1-47e8-b5bd-073c4b684a92', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:50:46 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'b6f3e70a-b207-418f-b42d-dd0595d00ee7', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:50:46 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d25f70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d24080>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d5fb00>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e8c5c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e14b60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9d880>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125edbda0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0fc50>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0d910>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d454c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d465d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d45d30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea2c60>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea3260>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '0714295c-ac72-4062-a3d2-5963102e1465', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:51:19 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e8c620>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ff3d40>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d44b60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d47320>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0e8d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0f1d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'acd66993-0166-453d-aea9-70323c6d09f8', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:51:25 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ff29c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d46630>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0f260>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0d910>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125eda210>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d249e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e3df40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea0f20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea04d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea1b80>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea2720>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea18e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea2c60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d7dc70>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '0db91006-971b-4ed3-9eb5-be12d1c5375a', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:51:43 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'e12cd6ca-9377-4674-a947-3949e6b0a7c3', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:51:43 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 3/10 - Reward: 0.426, Length: 1, Avg Reward (10): 0.426, Time: 85.1s, Pattern: periodic
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: ramp
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '048b3cf3-6b23-417b-83cc-ea30b7a1a071', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:51:44 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea4a70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c55700>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d26180>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125de3410>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea1190>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec3f50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec3b30>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec2240>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e16090>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e14fe0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e14590>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e15be0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e15700>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125de2f30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'b5b8d609-46c9-4a70-b33d-adb8918536ca', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:52:12 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '487418fb-611c-4a02-98b3-2a6d7fbcdb66', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:52:12 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125cb42f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c2e16d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c98170>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea4380>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d25100>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec0530>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec1610>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec1ee0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec1670>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c55f40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e14aa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e15370>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e14e00>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec0530>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '157761f1-de9e-4cc4-8794-ebc7703f2ed3', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:52:45 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec1490>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea1640>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea1b20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea4530>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea7d40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c56630>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'a750a42e-ba1e-4fa1-b4fc-3e5f7e544677', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:52:51 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea4380>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c98e00>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c83c20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d26510>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d27a10>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec0c20>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec01a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec37a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec32c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c560f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e15be0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e15eb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e16510>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e148c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '20bc5b5e-03b2-4906-9e11-130a32db0f3f', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:53:09 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'e5e0334e-4453-461c-aa01-9e5a182e7f8c', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:53:09 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 4/10 - Reward: 0.536, Length: 1, Avg Reward (10): 0.454, Time: 85.1s, Pattern: ramp
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: random
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '90b9bdb2-a500-44b4-9e4d-703238306f26', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:53:10 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0c530>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e142f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ef7c20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ebc5c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ebd0a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec16a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea1010>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d24770>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01190>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01940>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b001d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01790>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b1ede0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b1e870>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'fb18093c-3ce1-445b-878d-b94057e13bcc', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:53:38 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'dce6cd36-338a-4ef5-af91-34c2e2a5d801', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:53:38 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d279b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d7f650>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c986e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125f9dd60>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ebd790>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ebd310>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea0830>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ec20f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b00cb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b03a10>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b03d40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b03230>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b1f080>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e3e0f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'ed0732a9-d474-4e8e-97f0-3bc2bb8938cb', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:54:11 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c560f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b02450>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01550>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b02b70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b02c60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01940>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '2470210f-8f93-4ae0-9997-5907173c02ca', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:54:17 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125fc3bf0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ebf260>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea2000>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ef4260>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b03290>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b034d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b03c20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b02240>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01490>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b018e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e3e0f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b1e840>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b1e090>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b1e600>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '62f309ec-3e9f-45c9-9ab2-5f0e292ad004', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:54:35 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '0e7ca3ba-99e3-46fa-a699-7c6adc19d979', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:54:35 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 5/10 - Reward: 0.346, Length: 1, Avg Reward (10): 0.432, Time: 85.1s, Pattern: random
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: periodic
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '488cc555-844a-4e05-8974-9933b9efa2d3', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:54:36 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125bdf230>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b6eb40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d5fe30>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a77ec0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aaa210>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa8440>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa8cb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125bc80e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a93e60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea0740>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01ac0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a401a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aba840>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ab9f70>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'dd38405c-6282-4830-9f8c-317583022fcd', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:55:04 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'a7d5d52d-d886-4486-8455-f84fc91cbe9e', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:55:04 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea20c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a40470>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a92660>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a92c60>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a933b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea6d20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125bca600>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125fc0cb0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa8bf0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa8710>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa8e30>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa8470>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a74290>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b01ac0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '8acd50e9-905a-4e10-b585-077d2d1db55d', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:55:37 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a75310>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a43a70>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a936b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a93440>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125bfed20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a324e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '26093e92-b1f6-43f0-8f57-c856859f7780', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:55:43 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a40170>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d47bc0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a90c20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a93e60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d5fc20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa9fa0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa8b00>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aab2c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aab020>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aab200>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a5e4b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b1d220>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a776b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a758e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '6ff9bc73-4d79-4e72-ab4d-1266fdde9b1a', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:56:01 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'e4e79f2b-aef3-4d4f-81f8-9e6ca2aa6809', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:56:01 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 6/10 - Reward: 0.426, Length: 1, Avg Reward (10): 0.431, Time: 85.1s, Pattern: periodic
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: ramp
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'e4492721-8cf3-4bef-8357-af6b009b010b', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:56:02 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259f8860>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea75c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12598c9e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125920fb0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125804f50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259d9520>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259d8a40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b9f860>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12582ab70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12582bc50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125828b60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12582be60>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125843ec0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e17950>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '9860b97d-3aa0-4755-a3de-2296ccd0ea2a', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:56:30 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '84097508-b7c3-4b20-b24f-0bedb4bc5116', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:56:30 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125c560f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12594eb40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259baba0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259db920>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12582bfb0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125829be0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125829f70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125828680>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125e14860>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125806000>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125804f50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125843ec0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125843e30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258073e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '25e75983-637e-4e46-be2f-31a8dbde112d', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:57:03 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b9fe30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a40080>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259db350>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125840620>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125841d90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125843740>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'c6bb48a7-7c59-41b6-9ce6-37045b64de74', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:57:09 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259829c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259a4da0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a92330>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125829790>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125828c80>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125828050>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125829880>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259db920>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259db4a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258411f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258434d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125842390>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125842420>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125840470>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '67652e50-6c7f-4631-ab46-6ef07051b23e', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:57:27 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'e65fcbcd-003d-4342-91a9-5d59380fc2c8', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:57:27 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 7/10 - Reward: 0.536, Length: 1, Avg Reward (10): 0.446, Time: 85.1s, Pattern: ramp
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: periodic
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '221265d2-2e06-471b-a957-51466f669b7c', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:57:28 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125829580>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258a3f20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125778080>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12573cf20>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12579a0c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12579a6c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257aef90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257af260>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257af290>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258423f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125bdf2c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257caf30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257ca720>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257c90a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '962ec21d-8673-4cbb-a51a-c5172e40ff90', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:57:56 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'ba9df5b6-4ee0-4b9f-a265-7c7af0b64a17', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:57:56 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125841190>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125841a90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12573ff80>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257aec00>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257af8c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257af3b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257ad370>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258a1280>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa9b20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12572a510>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12579b260>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12579be90>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257caff0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125778110>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '5c0330b3-a6eb-4ddb-bb30-0e5c43dac1a1', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:58:29 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125b02d80>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125728a40>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa9700>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea30e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257afaa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257ada30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'fd16481c-50d6-486b-a3fb-7bac1ccd452c', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:58:35 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258423f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257add00>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257ac110>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257ac1d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257afe60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125aa9700>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12579baa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12579b890>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125a5fda0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125806fc0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125779130>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257c9220>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257c8e30>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257c9010>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'cd7b66b4-9d4e-4461-8b82-25c9336f1d6f', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:58:53 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '7d682ada-8bf4-4e75-948f-39d38baa6a54', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:58:53 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 8/10 - Reward: 0.426, Length: 1, Avg Reward (10): 0.444, Time: 85.1s, Pattern: periodic
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: random
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '38a2b401-56d9-4ea7-8190-1da8fa54683d', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:58:53 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12598fec0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12565aa50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125728b90>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1256e1c40>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257c9130>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1256ce2d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553a0c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1255394f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125554a70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125554590>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125555100>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257af260>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125535460>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125539220>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'a66bc58f-5308-4f4f-88f2-bb5b177bbfb3', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:59:22 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '909c28d4-4348-43b3-b47b-23c8717544de', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:59:22 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1259fbce0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125729b20>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1256cc800>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257cb6b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125538fe0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125539c70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125539970>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553a9f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125659730>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12563bce0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125555910>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125688260>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ea7890>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257c9490>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'cc633736-e059-48a5-a75d-c081bbd8cd63', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 01:59:55 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1255576b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125afa3c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553a0c0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553a420>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553a900>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553a9f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': 'c24a8aad-f3cb-4e77-9c61-e3746cf198e2', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:00:01 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d267b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1255390d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125539670>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125539220>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553ab70>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1256cc830>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257c9490>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1255578f0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125556b10>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125557f50>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1255556a0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125659700>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12582ae10>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12563be30>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '61dbaa5f-08c1-4705-a854-873f77d64bcb', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:00:19 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '61f06ea1-d885-4fed-972d-f1f5b7133f7b', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:00:19 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 9/10 - Reward: 0.346, Length: 1, Avg Reward (10): 0.433, Time: 85.1s, Pattern: random
INFO:kubernetes_env:Resetting RL environment for new episode
INFO:kubernetes_env:Episode load pattern: spike
ERROR:kubernetes_env:Error resetting deployments: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '6b58dd43-8c73-4579-8096-4fa45db07d3c', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:00:19 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125d0ede0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12c542930>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125778fe0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12480db20>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa124851c40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248c7aa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ab9760>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248fc140>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248fef00>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248fdc10>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125555340>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125ebf620>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa124715ac0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa124714e00>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '017c91b0-f609-438d-87eb-4eb319c2b1ff', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:00:47 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error executing action: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '9f8917f5-0cd0-42a4-ba35-cdab3adcd2da', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:00:47 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125557aa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248c7aa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248c4f50>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248d94f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248db9b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa124885be0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12553a330>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12480d790>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125534410>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248531d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1255d6060>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1258433b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248fc890>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125841bb0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '4651dc17-9685-47bf-82ca-f71b67e4c386', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:01:20 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1257ae0f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248c7200>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248c7aa0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12480d2b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248ff530>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248fd910>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '4abdd191-2946-446a-896a-4d23bf7b2f5e', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:01:26 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting latency: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12480db20>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting throughput: HTTPConnectionPool(host='localhost', port=8089): Max retries exceeded with url: /stats/requests (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248873e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248d8bc0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248d9220>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa12568ae40>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29
ERROR:kubernetes_env:Error getting CPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=avg%28100+-+%28avg+by+%28instance%29+%28rate%28node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D%29%29+%2A+100%29%29 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa124832510>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa125557530>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa124853200>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1256e18e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100
ERROR:kubernetes_env:Error getting memory utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=%281+-+%28node_memory_MemAvailable_bytes+%2F+node_memory_MemTotal_bytes%29%29+%2A+100 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248ff650>: Failed to establish a new connection: [Errno 111] Connection refused'))
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248ff3b0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248ff9e0>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248fcb60>: Failed to establish a new connection: [Errno 111] Connection refused')': /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL
ERROR:kubernetes_env:Error getting GPU utilization: HTTPConnectionPool(host='localhost', port=9090): Max retries exceeded with url: /api/v1/query?query=DCGM_FI_DEV_GPU_UTIL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fa1248fd190>: Failed to establish a new connection: [Errno 111] Connection refused'))
ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '715b962b-2c66-4efc-b711-bc193221bcf6', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:01:44 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


ERROR:kubernetes_env:Error getting replica counts: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'Audit-Id': '57612031-587f-4327-add1-120665ca52ba', 'Cache-Control': 'no-cache, private', 'Content-Type': 'application/json', 'X-Kubernetes-Pf-Flowschema-Uid': '129c3cf3-653d-4e64-81dc-9c65e6a5e0cc', 'X-Kubernetes-Pf-Prioritylevel-Uid': '6b536f38-699e-4959-9539-a24816cc3012', 'Date': 'Sat, 24 May 2025 02:01:45 GMT', 'Content-Length': '268'})
HTTP response body: {"kind":"Status","apiVersion":"v1","metadata":{},"status":"Failure","message":"deployments.apps \"mobilenetv4-triton-cpu-deployment\" not found","reason":"NotFound","details":{"name":"mobilenetv4-triton-cpu-deployment","group":"apps","kind":"deployments"},"code":404}


INFO:__main__:Episode 10/10 - Reward: 0.100, Length: 1, Avg Reward (10): 0.399, Time: 85.1s, Pattern: spike
INFO:ppo_agent:Model saved to /home/<USER>/allProjects/ecrl/experiments/rl/training_20250523_214725/model_episode_10.pt
INFO:ppo_agent:Model saved to /home/<USER>/allProjects/ecrl/experiments/rl/training_20250523_214725/best_model.pt
INFO:__main__:New best model saved with reward: 0.100
INFO:__main__:Training completed!
INFO:__main__:Performing final evaluation...
Traceback (most recent call last):
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/train_rl_agent.py", line 388, in <module>
    main()
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/train_rl_agent.py", line 385, in main
    trainer.train(num_episodes=args.episodes)
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/train_rl_agent.py", line 136, in train
    self._final_evaluation()
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/train_rl_agent.py", line 327, in _final_evaluation
    self.agent.load_model(best_model_path)
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/ppo_agent.py", line 418, in load_model
    checkpoint = torch.load(filepath, map_location=self.device)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/torch/serialization.py", line 1524, in load
    raise pickle.UnpicklingError(_get_wo_message(str(e))) from None
_pickle.UnpicklingError: Weights only load failed. This file can still be loaded, to do so you have two options, [1mdo those steps only if you trust the source of the checkpoint[0m. 
	(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.
	(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.
	WeightsUnpickler error: Unsupported global: GLOBAL ppo_agent.PPOConfig was not an allowed global by default. Please use `torch.serialization.add_safe_globals([ppo_agent.PPOConfig])` or the `torch.serialization.safe_globals([ppo_agent.PPOConfig])` context manager to allowlist this global if you trust this class/function.

Check the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.
