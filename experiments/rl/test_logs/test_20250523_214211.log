Traceback (most recent call last):
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/kubernetes_env.py", line 70, in __init__
    k8s_config.load_incluster_config()
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/incluster_config.py", line 121, in load_incluster_config
    try_refresh_token=try_refresh_token).load_and_set(client_configuration)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/incluster_config.py", line 54, in load_and_set
    self._load_config()
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/incluster_config.py", line 62, in _load_config
    raise ConfigException("Service host/port is not set.")
kubernetes.config.config_exception.ConfigException: Service host/port is not set.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/train_rl_agent.py", line 388, in <module>
    main()
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/train_rl_agent.py", line 384, in main
    trainer = RLTrainer(config)
              ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/train_rl_agent.py", line 57, in __init__
    self.env = KubernetesRLEnvironment(env_config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/allProjects/ecrl/experiments/scripts/rl/kubernetes_env.py", line 73, in __init__
    k8s_config.load_kube_config()
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/kube_config.py", line 819, in load_kube_config
    loader = _get_kube_config_loader(
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/kube_config.py", line 779, in _get_kube_config_loader
    return KubeConfigLoader(
           ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/kube_config.py", line 206, in __init__
    self.set_active_context(active_context)
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/kube_config.py", line 258, in set_active_context
    context_name = self._config['current-context']
                   ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/allProjects/ecrl/.venv/lib/python3.12/site-packages/kubernetes/config/kube_config.py", line 625, in __getitem__
    raise ConfigException(
kubernetes.config.config_exception.ConfigException: Invalid kube-config file. Expected key current-context in /home/<USER>/.kube/config
