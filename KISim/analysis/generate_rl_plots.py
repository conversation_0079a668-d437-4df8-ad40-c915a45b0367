#!/usr/bin/env python3
"""
Generate academic-style RL training plots for K<PERSON>im
Creates individual plots matching the academic paper style shown in the image.
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Optional

class RLPlotGenerator:
    """Generate academic-style RL training plots"""
    
    def __init__(self, output_dir: str = "./figures/rl"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set matplotlib style for academic papers
        plt.style.use('default')
        plt.rcParams.update({
            'font.size': 14,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'figure.titlesize': 18
        })
    
    def generate_synthetic_rl_data(self):
        """Generate synthetic RL training data for demonstration"""
        episodes = 100
        
        # Generate episode rewards with learning curve
        base_reward = -2.0
        improvement_rate = 0.03
        noise_level = 0.3
        
        episode_rewards = []
        for ep in range(episodes):
            # Learning curve: exponential improvement with noise
            reward = base_reward + (2.5 * (1 - np.exp(-improvement_rate * ep))) + np.random.normal(0, noise_level)
            episode_rewards.append(reward)
        
        # Generate training losses
        policy_losses = []
        value_losses = []
        for ep in range(episodes):
            policy_loss = 0.8 * np.exp(-0.02 * ep) + np.random.normal(0, 0.05)
            value_loss = 1.2 * np.exp(-0.015 * ep) + np.random.normal(0, 0.08)
            policy_losses.append(max(0, policy_loss))
            value_losses.append(max(0, value_loss))
        
        # Generate pattern-specific performance
        patterns = ['random', 'ramp', 'spike', 'periodic']
        pattern_performance = {}
        base_performances = [1.8, 2.1, 2.3, 1.9]  # Different base performance for each pattern
        
        for i, pattern in enumerate(patterns):
            pattern_rewards = []
            base_perf = base_performances[i]
            for _ in range(20):  # 20 episodes per pattern
                reward = base_perf + np.random.normal(0, 0.2)
                pattern_rewards.append(reward)
            pattern_performance[pattern] = pattern_rewards
        
        # Generate reward distribution
        reward_bins = np.arange(1.0, 3.0, 0.2)
        reward_counts = []
        for i in range(len(reward_bins)-1):
            count = np.random.randint(5, 25)
            reward_counts.append(count)
        
        return {
            'episode_rewards': episode_rewards,
            'policy_losses': policy_losses,
            'value_losses': value_losses,
            'pattern_performance': pattern_performance,
            'reward_distribution': {
                'bins': reward_bins,
                'counts': reward_counts
            }
        }
    
    def plot_training_progress(self, episode_rewards: List[float]):
        """Generate (a) RL Training Progress plot"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        episodes = range(1, len(episode_rewards) + 1)
        
        # Plot episode rewards
        ax.plot(episodes, episode_rewards, alpha=0.6, linewidth=1, color='steelblue', label='Episode Rewards')
        
        # Moving average
        window = 10
        if len(episode_rewards) >= window:
            moving_avg = np.convolve(episode_rewards, np.ones(window)/window, mode='valid')
            ax.plot(range(window, len(episode_rewards) + 1), moving_avg, 
                   color='red', linewidth=2.5, label='Moving Avg (10)')
        
        ax.set_xlabel('Episode')
        ax.set_ylabel('Reward')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'training_progress.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Training progress plot saved to: {self.output_dir}/training_progress.png")
    
    def plot_performance_by_pattern(self, pattern_performance: Dict[str, List[float]]):
        """Generate (b) Performance by Load Pattern plot"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        patterns = list(pattern_performance.keys())
        avg_rewards = [np.mean(pattern_performance[p]) for p in patterns]
        
        bars = ax.bar(patterns, avg_rewards, alpha=0.7, color='steelblue')
        
        # Add value labels on bars
        for bar, avg in zip(bars, avg_rewards):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                   f'{avg:.2f}', ha='center', va='bottom', fontsize=12)
        
        ax.set_ylabel('Average Reward')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'performance_by_pattern.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Performance by pattern plot saved to: {self.output_dir}/performance_by_pattern.png")
    
    def plot_training_losses(self, policy_losses: List[float], value_losses: List[float]):
        """Generate (c) Training Losses plot"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        episodes = range(1, len(policy_losses) + 1)
        
        ax.plot(episodes, policy_losses, label='Policy Loss', color='orange', linewidth=2)
        ax.plot(episodes, value_losses, label='Value Loss', color='purple', linewidth=2)
        
        ax.set_xlabel('Episode')
        ax.set_ylabel('Loss')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'training_losses.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Training losses plot saved to: {self.output_dir}/training_losses.png")
    
    def plot_learning_progress_by_pattern(self, pattern_performance: Dict[str, List[float]]):
        """Generate (a) Learning Progress by Pattern plot (bottom left)"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        patterns = list(pattern_performance.keys())
        colors = ['green', 'blue', 'red', 'orange']
        markers = ['o', 's', '^', 'D']
        
        for i, pattern in enumerate(patterns):
            rewards = pattern_performance[pattern]
            episodes = range(1, len(rewards) + 1)
            ax.plot(episodes, rewards, color=colors[i], marker=markers[i], 
                   label=pattern, linewidth=2, markersize=4, alpha=0.8)
        
        ax.set_xlabel('Episode')
        ax.set_ylabel('Reward')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'learning_progress_by_pattern.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Learning progress by pattern plot saved to: {self.output_dir}/learning_progress_by_pattern.png")
    
    def plot_reward_distribution(self, reward_data: Dict):
        """Generate (c) Reward Distribution plot (bottom right)"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        bins = reward_data['bins']
        counts = reward_data['counts']
        
        # Create histogram
        ax.bar(bins[:-1], counts, width=np.diff(bins), alpha=0.7, color='steelblue', 
               align='edge', edgecolor='black', linewidth=0.5)
        
        ax.set_xlabel('Reward')
        ax.set_ylabel('Frequency')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'reward_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Reward distribution plot saved to: {self.output_dir}/reward_distribution.png")
    
    def generate_all_rl_plots(self):
        """Generate all RL plots matching the academic paper style"""
        print("Generating synthetic RL training data...")
        data = self.generate_synthetic_rl_data()
        
        print("Generating RL training plots...")
        
        # Generate individual plots
        self.plot_training_progress(data['episode_rewards'])
        self.plot_performance_by_pattern(data['pattern_performance'])
        self.plot_training_losses(data['policy_losses'], data['value_losses'])
        self.plot_learning_progress_by_pattern(data['pattern_performance'])
        self.plot_reward_distribution(data['reward_distribution'])
        
        print(f"\nAll RL plots saved to: {self.output_dir}")
        print("Generated plots:")
        print("  - training_progress.png (Episode rewards with moving average)")
        print("  - performance_by_pattern.png (Performance by load pattern)")
        print("  - training_losses.png (Policy and value losses)")
        print("  - learning_progress_by_pattern.png (Learning progress by pattern)")
        print("  - reward_distribution.png (Reward distribution histogram)")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='Generate RL training plots')
    parser.add_argument('--output-dir', default='./figures/rl', help='Output directory for plots')
    
    args = parser.parse_args()
    
    generator = RLPlotGenerator(args.output_dir)
    generator.generate_all_rl_plots()

if __name__ == "__main__":
    main()
