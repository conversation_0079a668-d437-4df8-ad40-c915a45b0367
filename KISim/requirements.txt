# Core Dependencies
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.5.0
seaborn>=0.11.0
scipy>=1.7.0

# Machine Learning
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0
gym>=0.21.0
stable-baselines3>=1.6.0

# Kubernetes and Cloud
kubernetes>=24.2.0
prometheus-api-client>=0.5.0
requests>=2.28.0

# Data Processing
json5>=0.9.0
pyyaml>=6.0
tqdm>=4.64.0

# Model Serving
tritonclient[http]>=2.24.0
huggingface-hub>=0.10.0
onnx>=1.12.0
onnxruntime>=1.12.0

# Load Testing
locust>=2.10.0

# Visualization and Analysis
plotly>=5.10.0
kaleido>=0.2.1
pillow>=9.2.0

# Development and Testing
pytest>=7.1.0
pytest-cov>=3.0.0
black>=22.6.0
flake8>=5.0.0
mypy>=0.971

# Logging and Monitoring
loguru>=0.6.0
psutil>=5.9.0

# Optional: CUDA support (uncomment if using GPU)
# torch>=1.12.0+cu118 --extra-index-url https://download.pytorch.org/whl/cu118
# torchvision>=0.13.0+cu118 --extra-index-url https://download.pytorch.org/whl/cu118
# torchaudio>=0.12.0+cu118 --extra-index-url https://download.pytorch.org/whl/cu118
